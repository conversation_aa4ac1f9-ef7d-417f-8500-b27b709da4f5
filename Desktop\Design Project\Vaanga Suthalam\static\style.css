/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    height: 100%;
    overflow: hidden;
}

html, body {
    height: 100vh;
    width: 100vw;
    overflow: hidden;
    position: fixed;
    top: 0;
    left: 0;
}

:root {
    --primary-color: #FF6B35;
    --secondary-color: #F7931E;
    --accent-color: #FFD700;
    --dark-red: #B71C1C;
    --light-orange: #FFF3E0;
    --text-dark: #1A1A1A;
    --text-light: #666666;
    --white: #FFFFFF;
    --gray-100: #F5F5F5;
    --gray-200: #E0E0E0;
    --gray-300: #BDBDBD;
    --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 8px 30px rgba(0, 0, 0, 0.2);
    --border-radius: 12px;
    --border-radius-large: 20px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #F8FAFC;
    color: var(--text-dark);
    line-height: 1.6;
    overflow-x: hidden;
    margin: 0;
    padding: 0;
    height: 100vh;
    width: 100vw;
}

/* Background Elements */
.bg-pattern {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(255, 107, 53, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(247, 147, 30, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(255, 215, 0, 0.05) 0%, transparent 50%);
    z-index: -2;
}

.bg-gradient {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg,
        rgba(255, 107, 53, 0.02) 0%,
        rgba(247, 147, 30, 0.02) 50%,
        rgba(255, 215, 0, 0.02) 100%);
    z-index: -1;
}

.app-container {
    display: flex;
    height: 100vh;
    width: 100vw;
    margin: 0;
    background: var(--white);
    border-radius: 0;
    box-shadow: none;
    overflow: hidden;
    position: fixed;
    top: 0;
    left: 0;
}

/* Sidebar Styles */
.sidebar {
    width: 320px;
    background: linear-gradient(180deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: var(--white);
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
}

.sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="temple" patternUnits="userSpaceOnUse" width="20" height="20"><path d="M10 2L15 8H5L10 2ZM3 8H17V18H3V8Z" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23temple)"/></svg>');
    z-index: 0;
}

.sidebar-header {
    padding: 30px 25px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    position: relative;
    z-index: 1;
}

.logo-container {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo-icon {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    backdrop-filter: blur(10px);
}

.logo-text h1 {
    font-family: 'Playfair Display', serif;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 2px;
}

.logo-text p {
    font-size: 0.85rem;
    opacity: 0.8;
    font-weight: 300;
}

.sidebar-content {
    flex: 1;
    padding: 25px;
    position: relative;
    z-index: 1;
}

.travel-stats {
    margin-bottom: 30px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 15px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-item:last-child {
    border-bottom: none;
}

.stat-item i {
    width: 35px;
    height: 35px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

.stat-item div {
    flex: 1;
}

.stat-number {
    display: block;
    font-size: 1.2rem;
    font-weight: 600;
    line-height: 1.2;
}

.stat-label {
    font-size: 0.8rem;
    opacity: 0.8;
}

.quick-actions h3 {
    font-size: 1rem;
    margin-bottom: 20px;
    opacity: 0.9;
    font-weight: 500;
}

.action-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
}

.action-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: var(--white);
    padding: 15px 12px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    font-size: 0.8rem;
    backdrop-filter: blur(10px);
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.action-btn i {
    font-size: 1.2rem;
}

/* Agricultural Analysis Section */
.agri-analysis {
    margin-top: 30px;
    padding: 20px;
    background: rgba(76, 175, 80, 0.1);
    border-radius: var(--border-radius);
    border: 1px solid rgba(76, 175, 80, 0.2);
}

.agri-analysis h3 {
    font-size: 1rem;
    margin-bottom: 10px;
    color: #2E7D32;
    font-weight: 600;
}

.agri-analysis p {
    font-size: 0.85rem;
    opacity: 0.9;
    margin-bottom: 15px;
    color: #4CAF50;
}

.agri-upload-btn {
    width: 100%;
    background: linear-gradient(135deg, #4CAF50, #66BB6A);
    border: none;
    color: var(--white);
    padding: 12px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-weight: 500;
}

.agri-upload-btn:hover {
    background: linear-gradient(135deg, #388E3C, #4CAF50);
    transform: translateY(-1px);
}

/* Main Chat Area */
.chat-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: var(--white);
    position: relative;
    height: 100vh;
    overflow: hidden;
}

/* Chat Header */
.chat-header {
    padding: 20px 30px;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--white);
    position: sticky;
    top: 0;
    z-index: 10;
}

.chat-header-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.bot-avatar {
    width: 45px;
    height: 45px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.2rem;
    position: relative;
}

.status-indicator {
    width: 12px;
    height: 12px;
    background: #4CAF50;
    border: 2px solid var(--white);
    border-radius: 50%;
    position: absolute;
    bottom: -2px;
    right: -2px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.7; }
    100% { transform: scale(1); opacity: 1; }
}

.bot-info h2 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 2px;
    color: var(--text-dark);
}

.bot-status {
    font-size: 0.85rem;
    color: var(--text-light);
    display: flex;
    align-items: center;
    gap: 6px;
}

.bot-status i {
    color: #4CAF50;
    font-size: 0.6rem;
}

.chat-actions {
    display: flex;
    gap: 10px;
}

.action-icon {
    width: 40px;
    height: 40px;
    border: none;
    background: var(--gray-100);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    color: var(--text-light);
}

.action-icon:hover {
    background: var(--gray-200);
    color: var(--text-dark);
}

/* Welcome Card */
.welcome-card {
    margin: 30px;
    padding: 40px;
    background: linear-gradient(135deg, var(--light-orange) 0%, rgba(255, 215, 0, 0.1) 100%);
    border-radius: var(--border-radius-large);
    border: 1px solid rgba(255, 107, 53, 0.1);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.welcome-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 107, 53, 0.05) 0%, transparent 70%);
    animation: rotate 20s linear infinite;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.welcome-content {
    position: relative;
    z-index: 1;
}

.welcome-icon {
    width: 60px;
    height: 60px;
    background: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: var(--white);
    font-size: 1.5rem;
}

.welcome-content h2 {
    font-family: 'Playfair Display', serif;
    font-size: 1.8rem;
    color: var(--dark-red);
    margin-bottom: 15px;
    font-weight: 600;
}

.welcome-content p {
    color: var(--text-light);
    margin-bottom: 30px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.welcome-suggestions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 30px;
}

.suggestion-card {
    background: var(--white);
    padding: 20px;
    border-radius: var(--border-radius);
    border: 1px solid var(--gray-200);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    text-align: center;
}

.suggestion-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
    border-color: var(--primary-color);
}

.suggestion-card i {
    font-size: 1.5rem;
    color: var(--primary-color);
}

.suggestion-card span {
    font-weight: 500;
    color: var(--text-dark);
    font-size: 0.9rem;
}

/* Chat Messages Container */
.chat-messages-container {
    flex: 1;
    overflow-y: auto;
    padding: 0 30px;
    scroll-behavior: smooth;
    height: calc(100vh - 140px); /* Adjust based on header and input heights */
    min-height: 0; /* Important for flex child to shrink */
}

.chat-messages {
    padding: 20px 0;
    min-height: 100%;
}

.message {
    margin-bottom: 25px;
    display: flex;
    align-items: flex-start;
    gap: 12px;
    animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.message.user {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    flex-shrink: 0;
    position: relative;
}

.message.user .message-avatar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: var(--white);
}

.message.bot .message-avatar {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
}

.message-content {
    max-width: 75%;
    padding: 16px 20px;
    border-radius: 18px;
    line-height: 1.6;
    word-wrap: break-word;
    position: relative;
    font-size: 0.95rem;
}

.message.user .message-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: var(--white);
    border-bottom-right-radius: 6px;
}

.message.bot .message-content {
    background: var(--gray-100);
    color: var(--text-dark);
    border-bottom-left-radius: 6px;
    border: 1px solid var(--gray-200);
}

.message-content strong {
    font-weight: 600;
}

.message-content em {
    font-style: italic;
    color: inherit;
}

.message-content a {
    color: inherit;
    text-decoration: underline;
}

.message.user .message-content a {
    color: rgba(255, 255, 255, 0.9);
}

.message-time {
    font-size: 0.75rem;
    color: var(--text-light);
    margin-top: 5px;
    text-align: right;
}

.message.user .message-time {
    text-align: left;
}

/* Typing Indicator */
.typing-indicator {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 20px 0;
    animation: messageSlideIn 0.3s ease-out;
}

.typing-avatar {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    flex-shrink: 0;
}

.typing-content {
    background: var(--gray-100);
    border: 1px solid var(--gray-200);
    border-radius: 18px;
    border-bottom-left-radius: 6px;
    padding: 16px 20px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.typing-dots {
    display: flex;
    gap: 4px;
}

.typing-dots span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--primary-color);
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }
.typing-dots span:nth-child(3) { animation-delay: 0s; }

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1.2);
        opacity: 1;
    }
}

.typing-text {
    font-size: 0.9rem;
    color: var(--text-light);
    font-style: italic;
}

/* Chat Input Area */
.chat-input-area {
    padding: 20px 30px 30px;
    background: var(--white);
    border-top: 1px solid var(--gray-200);
}

.input-container {
    margin-bottom: 15px;
}

.input-wrapper {
    display: flex;
    align-items: flex-end;
    gap: 12px;
    background: var(--gray-100);
    border: 2px solid transparent;
    border-radius: var(--border-radius-large);
    padding: 12px 16px;
    transition: var(--transition);
}

.input-wrapper:focus-within {
    border-color: var(--primary-color);
    background: var(--white);
    box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
}

.attachment-btn {
    width: 35px;
    height: 35px;
    border: none;
    background: var(--gray-200);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
    color: var(--text-light);
    flex-shrink: 0;
}

.attachment-btn:hover {
    background: var(--primary-color);
    color: var(--white);
}

/* Image Preview Container */
.image-preview-container {
    margin-bottom: 15px;
    padding: 15px;
    background: var(--gray-100);
    border-radius: var(--border-radius);
    border: 2px dashed var(--gray-300);
}

.image-preview {
    display: flex;
    align-items: center;
    gap: 15px;
}

.image-preview img {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 8px;
    border: 2px solid var(--white);
    box-shadow: var(--shadow-light);
}

.image-info {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.image-name {
    font-size: 0.9rem;
    color: var(--text-dark);
    font-weight: 500;
}

.remove-image {
    width: 30px;
    height: 30px;
    border: none;
    background: #f44336;
    color: var(--white);
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.remove-image:hover {
    background: #d32f2f;
    transform: scale(1.1);
}

#messageInput {
    flex: 1;
    border: none;
    background: transparent;
    font-size: 1rem;
    line-height: 1.5;
    resize: none;
    outline: none;
    font-family: inherit;
    color: var(--text-dark);
    min-height: 24px;
    max-height: 120px;
}

#messageInput::placeholder {
    color: var(--text-light);
}

.input-actions {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-shrink: 0;
}

.char-count {
    font-size: 0.8rem;
    color: var(--text-light);
    font-weight: 500;
}

#sendButton {
    width: 40px;
    height: 40px;
    border: none;
    background: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

#sendButton:hover:not(:disabled) {
    background: var(--secondary-color);
    transform: scale(1.05);
}

#sendButton:disabled {
    background: var(--gray-300);
    cursor: not-allowed;
    transform: none;
}

/* Quick Replies */
.quick-replies {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.quick-reply {
    background: var(--white);
    border: 1px solid var(--gray-200);
    color: var(--text-dark);
    padding: 8px 16px;
    border-radius: 20px;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.9rem;
    font-weight: 500;
}

.quick-reply:hover {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

/* Floating Action Menu */
.floating-menu {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 100;
}

.fab-main {
    width: 60px;
    height: 60px;
    border: none;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    box-shadow: var(--shadow-medium);
    position: relative;
    z-index: 101;
}

.fab-main:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-heavy);
}

.fab-options {
    position: absolute;
    bottom: 70px;
    right: 0;
    display: flex;
    flex-direction: column;
    gap: 10px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: var(--transition);
}

.floating-menu.active .fab-options {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.fab-option {
    width: 45px;
    height: 45px;
    border: none;
    background: var(--white);
    color: var(--primary-color);
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    box-shadow: var(--shadow-light);
    border: 1px solid var(--gray-200);
}

.fab-option:hover {
    background: var(--primary-color);
    color: var(--white);
    transform: scale(1.1);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-content {
    text-align: center;
    color: var(--white);
}

.loading-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.2);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-content h3 {
    font-size: 1.2rem;
    margin-bottom: 10px;
    font-weight: 600;
}

.loading-content p {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Image Upload Modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background: var(--white);
    border-radius: var(--border-radius-large);
    box-shadow: var(--shadow-heavy);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow: hidden;
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    padding: 20px 25px;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, #4CAF50, #66BB6A);
    color: var(--white);
}

.modal-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0;
}

.modal-close {
    width: 35px;
    height: 35px;
    border: none;
    background: rgba(255, 255, 255, 0.2);
    color: var(--white);
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.modal-body {
    padding: 25px;
}

.upload-area {
    border: 3px dashed var(--gray-300);
    border-radius: var(--border-radius);
    padding: 40px 20px;
    text-align: center;
    transition: var(--transition);
    cursor: pointer;
    margin-bottom: 25px;
}

.upload-area:hover,
.upload-area.dragover {
    border-color: #4CAF50;
    background: rgba(76, 175, 80, 0.05);
}

.upload-content i {
    font-size: 3rem;
    color: #4CAF50;
    margin-bottom: 15px;
}

.upload-content h4 {
    font-size: 1.1rem;
    color: var(--text-dark);
    margin-bottom: 10px;
    font-weight: 600;
}

.upload-content p {
    color: var(--text-light);
    margin-bottom: 5px;
}

.upload-note {
    font-size: 0.8rem !important;
    color: var(--text-light) !important;
    margin-bottom: 20px !important;
}

.upload-btn {
    background: #4CAF50;
    color: var(--white);
    border: none;
    padding: 12px 25px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.upload-btn:hover {
    background: #388E3C;
    transform: translateY(-1px);
}

.analysis-options {
    margin-top: 20px;
}

.analysis-options h4 {
    font-size: 1rem;
    color: var(--text-dark);
    margin-bottom: 15px;
    font-weight: 600;
}

.option-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.option-btn {
    flex: 1;
    min-width: 120px;
    padding: 12px 15px;
    border: 2px solid var(--gray-200);
    background: var(--white);
    color: var(--text-dark);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    font-size: 0.85rem;
    font-weight: 500;
}

.option-btn:hover {
    border-color: #4CAF50;
    background: rgba(76, 175, 80, 0.05);
}

.option-btn.active {
    border-color: #4CAF50;
    background: #4CAF50;
    color: var(--white);
}

.option-btn i {
    font-size: 1.2rem;
}

.option-btn span {
    display: block;
    font-size: 0.8rem;
    margin-top: 4px;
}

.analysis-description {
    margin-top: 15px;
    padding: 12px 16px;
    background: rgba(255, 107, 53, 0.05);
    border: 1px solid rgba(255, 107, 53, 0.2);
    border-radius: var(--border-radius);
}

.analysis-description p {
    margin: 0;
    font-size: 0.85rem;
    color: var(--text-light);
    line-height: 1.4;
}

.modal-footer {
    padding: 20px 25px;
    border-top: 1px solid var(--gray-200);
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    background: var(--gray-100);
}

.btn-secondary {
    padding: 10px 20px;
    border: 1px solid var(--gray-300);
    background: var(--white);
    color: var(--text-dark);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-weight: 500;
}

.btn-secondary:hover {
    background: var(--gray-100);
}

.btn-primary {
    padding: 10px 20px;
    border: none;
    background: #4CAF50;
    color: var(--white);
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 8px;
}

.btn-primary:hover:not(:disabled) {
    background: #388E3C;
    transform: translateY(-1px);
}

.btn-primary:disabled {
    background: var(--gray-300);
    cursor: not-allowed;
    transform: none;
}

/* Mobile-First Responsive Design */
@media (max-width: 1024px) {
    .app-container {
        margin: 0;
        border-radius: 0;
        height: 100vh;
        width: 100vw;
    }

    .sidebar {
        width: 300px;
        box-shadow: 2px 0 10px rgba(0,0,0,0.1);
    }

    .welcome-suggestions {
        grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
        gap: 12px;
    }

    .suggestion-card {
        padding: 15px;
    }
}

@media (max-width: 768px) {
    body {
        background: var(--white);
        overflow: hidden;
        position: fixed;
        width: 100%;
        height: 100%;
    }

    .app-container {
        margin: 0;
        border-radius: 0;
        height: 100vh;
        width: 100vw;
        display: flex;
        flex-direction: column;
        position: fixed;
        top: 0;
        left: 0;
    }

    .sidebar {
        position: fixed;
        left: -100%;
        top: 0;
        height: 100vh;
        z-index: 1000;
        transition: left 0.3s ease-in-out;
        width: 85vw;
        max-width: 320px;
        box-shadow: 2px 0 20px rgba(0,0,0,0.3);
        overflow-y: auto;
    }

    .sidebar.active {
        left: 0;
    }

    .chat-main {
        width: 100%;
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    .chat-header {
        padding: 12px 16px;
        min-height: 60px;
        flex-shrink: 0;
    }

    .chat-header-info {
        gap: 12px;
    }

    .bot-avatar {
        width: 40px;
        height: 40px;
        font-size: 1.1rem;
    }

    .bot-info h2 {
        font-size: 1rem;
        line-height: 1.2;
    }

    .bot-status {
        font-size: 0.8rem;
    }

    .chat-actions {
        gap: 8px;
    }

    .action-icon {
        width: 36px;
        height: 36px;
        font-size: 0.9rem;
    }

    .welcome-card {
        margin: 15px;
        padding: 25px 20px;
    }

    .welcome-content h2 {
        font-size: 1.4rem;
        margin-bottom: 12px;
    }

    .welcome-icon {
        width: 50px;
        height: 50px;
        font-size: 1.3rem;
        margin-bottom: 15px;
    }

    .welcome-suggestions {
        grid-template-columns: 1fr 1fr;
        gap: 10px;
        margin-top: 20px;
    }

    .suggestion-card {
        padding: 15px 12px;
        min-height: 80px;
    }

    .suggestion-card i {
        font-size: 1.3rem;
        margin-bottom: 8px;
    }

    .suggestion-card span {
        font-size: 0.85rem;
        line-height: 1.2;
    }

    .chat-messages-container {
        padding: 0 15px;
        flex: 1;
        overflow-y: auto;
        height: calc(100vh - 120px); /* Adjust for mobile header and input */
        min-height: 0;
    }

    .message {
        margin-bottom: 20px;
    }

    .message-avatar {
        width: 32px;
        height: 32px;
        font-size: 0.9rem;
    }

    .message-content {
        max-width: 80%;
        font-size: 0.9rem;
        padding: 14px 16px;
    }

    .chat-input-area {
        padding: 12px 15px 15px;
        flex-shrink: 0;
        background: var(--white);
        border-top: 1px solid var(--gray-200);
    }

    .input-wrapper {
        padding: 10px 12px;
    }

    .attachment-btn {
        width: 32px;
        height: 32px;
        font-size: 0.9rem;
    }

    #messageInput {
        font-size: 16px; /* Prevents zoom on iOS */
        line-height: 1.4;
    }

    #sendButton {
        width: 36px;
        height: 36px;
        font-size: 0.9rem;
    }

    .quick-replies {
        gap: 8px;
        margin-top: 10px;
        flex-wrap: wrap;
        justify-content: flex-start;
    }

    .quick-reply {
        padding: 6px 12px;
        font-size: 0.85rem;
        border-radius: 15px;
    }

    .floating-menu {
        bottom: 80px;
        right: 15px;
    }

    .fab-main {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .fab-option {
        width: 40px;
        height: 40px;
        font-size: 0.9rem;
    }

    /* Sidebar content adjustments */
    .sidebar-content {
        padding: 20px 15px;
    }

    .sidebar-header h2 {
        font-size: 1.3rem;
    }

    .stats-grid {
        grid-template-columns: 1fr 1fr;
        gap: 10px;
        margin: 15px 0;
    }

    .stat-item {
        padding: 12px;
    }

    .stat-number {
        font-size: 1.1rem;
    }

    .stat-label {
        font-size: 0.75rem;
    }

    .action-buttons {
        grid-template-columns: 1fr 1fr;
        gap: 8px;
    }

    .action-btn {
        padding: 12px 8px;
        font-size: 0.85rem;
    }

    .action-btn i {
        font-size: 1.1rem;
        margin-bottom: 4px;
    }

    /* Feature sections in sidebar */
    .heritage-section,
    .forest-analysis,
    .voice-assistant,
    .tree-identification,
    .tourist-guide,
    .itinerary-planner {
        margin-top: 15px;
        padding: 15px;
    }

    .heritage-section h3,
    .forest-analysis h3,
    .voice-assistant h3,
    .tree-identification h3,
    .tourist-guide h3,
    .itinerary-planner h3 {
        font-size: 0.9rem;
        margin-bottom: 8px;
    }

    .heritage-section p,
    .forest-analysis p,
    .voice-assistant p,
    .tree-identification p,
    .tourist-guide p,
    .itinerary-planner p {
        font-size: 0.8rem;
        margin-bottom: 12px;
    }

    .heritage-btn,
    .forest-upload-btn,
    .voice-btn,
    .tree-id-btn,
    .guide-btn,
    .itinerary-btn {
        padding: 10px;
        font-size: 0.85rem;
    }
}

@media (max-width: 480px) {
    .chat-header-info {
        gap: 10px;
    }

    .bot-avatar {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }

    .bot-info h2 {
        font-size: 1rem;
    }

    .bot-status {
        font-size: 0.8rem;
    }

    .welcome-card {
        margin: 15px;
        padding: 25px 15px;
    }

    .welcome-content h2 {
        font-size: 1.3rem;
    }

    .welcome-icon {
        width: 50px;
        height: 50px;
        font-size: 1.3rem;
    }

    .action-buttons {
        grid-template-columns: 1fr;
    }

    .quick-replies {
        justify-content: center;
    }

    /* Modal responsive adjustments */
    .modal-content {
        width: 95%;
        margin: 10px;
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 15px 20px;
    }

    .upload-area {
        padding: 30px 15px;
    }

    .upload-content i {
        font-size: 2.5rem;
    }

    .option-buttons {
        flex-direction: column;
    }

    .option-btn {
        min-width: auto;
    }

    .modal-footer {
        flex-direction: column;
    }

    .btn-secondary,
    .btn-primary {
        width: 100%;
        justify-content: center;
    }
}

/* Sidebar Overlay for Mobile */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
}

.sidebar-overlay.active {
    opacity: 1;
    visibility: visible;
}

/* Scrollbar Styling */
.chat-messages-container::-webkit-scrollbar {
    width: 6px;
}

.chat-messages-container::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: 3px;
}

.chat-messages-container::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: 3px;
}

.chat-messages-container::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

/* Focus States */
.action-btn:focus,
.suggestion-card:focus,
.quick-reply:focus,
.fab-main:focus,
.fab-option:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Animation for better UX */
.message-content,
.suggestion-card,
.quick-reply,
.action-btn {
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* Print Styles */
@media print {
    .sidebar,
    .chat-input-area,
    .floating-menu,
    .loading-overlay {
        display: none !important;
    }

    .chat-main {
        width: 100% !important;
    }

    .message-content {
        max-width: 100% !important;
        box-shadow: none !important;
        border: 1px solid #ccc !important;
    }
}

/* Tree Identification Section */
.tree-identification {
    margin-top: 20px;
    padding: 20px;
    background: linear-gradient(135deg, rgba(139, 69, 19, 0.1), rgba(160, 82, 45, 0.1));
    border-radius: var(--border-radius);
    border: 1px solid rgba(139, 69, 19, 0.2);
}

.tree-identification h3 {
    font-size: 1rem;
    margin-bottom: 10px;
    color: #8B4513;
    font-weight: 600;
}

.tree-identification p {
    font-size: 0.85rem;
    opacity: 0.9;
    margin-bottom: 15px;
    color: #A0522D;
}

.tree-id-btn {
    width: 100%;
    background: linear-gradient(135deg, #8B4513, #A0522D);
    border: none;
    color: var(--white);
    padding: 12px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-weight: 500;
}

.tree-id-btn:hover {
    background: linear-gradient(135deg, #654321, #8B4513);
    transform: translateY(-1px);
}

/* Tourist Guide Section */
.tourist-guide {
    margin-top: 20px;
    padding: 20px;
    background: linear-gradient(135deg, rgba(25, 118, 210, 0.1), rgba(30, 136, 229, 0.1));
    border-radius: var(--border-radius);
    border: 1px solid rgba(25, 118, 210, 0.2);
}

.tourist-guide h3 {
    font-size: 1rem;
    margin-bottom: 10px;
    color: #1976D2;
    font-weight: 600;
}

.tourist-guide p {
    font-size: 0.85rem;
    opacity: 0.9;
    margin-bottom: 15px;
    color: #1E88E5;
}

.guide-btn {
    width: 100%;
    background: linear-gradient(135deg, #1976D2, #1E88E5);
    border: none;
    color: var(--white);
    padding: 12px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-weight: 500;
}

.guide-btn:hover {
    background: linear-gradient(135deg, #1565C0, #1976D2);
    transform: translateY(-1px);
}

/* Itinerary Planner Section */
.itinerary-planner {
    margin-top: 20px;
    padding: 20px;
    background: linear-gradient(135deg, rgba(255, 152, 0, 0.1), rgba(255, 193, 7, 0.1));
    border-radius: var(--border-radius);
    border: 1px solid rgba(255, 152, 0, 0.2);
}

.itinerary-planner h3 {
    font-size: 1rem;
    margin-bottom: 10px;
    color: #F57C00;
    font-weight: 600;
}

.itinerary-planner p {
    font-size: 0.85rem;
    opacity: 0.9;
    margin-bottom: 15px;
    color: #FF9800;
}

.itinerary-btn {
    width: 100%;
    background: linear-gradient(135deg, #FF9800, #FFC107);
    border: none;
    color: var(--white);
    padding: 12px;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-weight: 500;
}

.itinerary-btn:hover {
    background: linear-gradient(135deg, #F57C00, #FF9800);
    transform: translateY(-1px);
}

/* Tourist Guide Modal */
.guide-modal {
    max-width: 900px;
    max-height: 85vh;
    overflow-y: auto;
}

.guide-search {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.guide-search input {
    flex: 1;
    padding: 12px 16px;
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius);
    font-size: 1rem;
    outline: none;
    transition: var(--transition);
}

.guide-search input:focus {
    border-color: var(--primary-color);
}

.search-btn {
    padding: 12px 16px;
    background: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.search-btn:hover {
    background: var(--secondary-color);
}

.district-categories {
    display: flex;
    gap: 10px;
    margin-bottom: 25px;
    flex-wrap: wrap;
}

.category-btn {
    padding: 8px 16px;
    border: 2px solid var(--gray-200);
    background: var(--white);
    color: var(--text-dark);
    border-radius: 20px;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.9rem;
    font-weight: 500;
}

.category-btn:hover {
    border-color: var(--primary-color);
    background: rgba(255, 107, 53, 0.05);
}

.category-btn.active {
    border-color: var(--primary-color);
    background: var(--primary-color);
    color: var(--white);
}

.districts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    max-height: 400px;
    overflow-y: auto;
}

.district-card {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--border-radius);
    padding: 20px;
    cursor: pointer;
    transition: var(--transition);
}

.district-card:hover {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-medium);
    transform: translateY(-2px);
}

.district-card h4 {
    font-size: 1.1rem;
    color: var(--text-dark);
    margin-bottom: 10px;
    font-weight: 600;
}

.district-card .region {
    background: var(--primary-color);
    color: var(--white);
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    display: inline-block;
    margin-bottom: 10px;
}

.district-card .attractions {
    font-size: 0.85rem;
    color: var(--text-light);
    line-height: 1.4;
}

/* Itinerary Modal */
.itinerary-modal {
    max-width: 600px;
}

.itinerary-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    font-weight: 500;
    color: var(--text-dark);
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.destinations-selector {
    display: flex;
    gap: 10px;
}

.destinations-selector input {
    flex: 1;
    padding: 10px 15px;
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius);
    font-size: 1rem;
    outline: none;
    transition: var(--transition);
}

.destinations-selector input:focus {
    border-color: var(--primary-color);
}

.add-destination-btn {
    padding: 10px 15px;
    background: var(--primary-color);
    color: var(--white);
    border: none;
    border-radius: var(--border-radius);
    cursor: pointer;
    transition: var(--transition);
}

.add-destination-btn:hover {
    background: var(--secondary-color);
}

.selected-destinations {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 10px;
}

.destination-tag {
    background: var(--primary-color);
    color: var(--white);
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.85rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.destination-tag .remove-destination {
    background: none;
    border: none;
    color: var(--white);
    cursor: pointer;
    font-size: 0.8rem;
    padding: 0;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.destination-tag .remove-destination:hover {
    background: rgba(255, 255, 255, 0.2);
}

.form-group select {
    padding: 10px 15px;
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius);
    font-size: 1rem;
    background: var(--white);
    color: var(--text-dark);
    cursor: pointer;
    transition: var(--transition);
}

.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
}

.interests-selector {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
}

.interest-checkbox {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 8px;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.interest-checkbox:hover {
    background: var(--gray-100);
}

.interest-checkbox input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: var(--primary-color);
}

/* Enhanced Mobile Styles for Better UX */
@media (max-width: 480px) {
    /* Override previous styles for better mobile experience */
    .chat-header {
        padding: 8px 12px;
        min-height: 50px;
        position: sticky;
        top: 0;
        z-index: 100;
        background: var(--white);
        border-bottom: 1px solid var(--gray-200);
    }

    .bot-info h2 {
        font-size: 0.85rem;
        font-weight: 600;
    }

    .bot-status {
        font-size: 0.7rem;
        opacity: 0.8;
    }

    .welcome-suggestions {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .suggestion-card {
        padding: 15px;
        min-height: auto;
        display: flex;
        align-items: center;
        gap: 12px;
        text-align: left;
    }

    .suggestion-card i {
        font-size: 1.5rem;
        margin-bottom: 0;
        flex-shrink: 0;
    }

    .suggestion-card span {
        font-size: 0.9rem;
        font-weight: 500;
    }

    /* Modal improvements for mobile */
    .modal-content {
        width: 95vw;
        max-width: none;
        margin: 10px auto;
        max-height: 90vh;
        border-radius: 12px;
    }

    .modal-header {
        padding: 15px;
        border-radius: 12px 12px 0 0;
    }

    .modal-header h3 {
        font-size: 1rem;
    }

    .modal-body {
        padding: 15px;
        max-height: 60vh;
        overflow-y: auto;
    }

    .modal-footer {
        padding: 15px;
        gap: 10px;
    }

    /* Upload area mobile optimization */
    .upload-area {
        padding: 25px 15px;
        border-width: 2px;
    }

    .upload-content i {
        font-size: 2rem;
        margin-bottom: 10px;
    }

    .upload-content h4 {
        font-size: 1rem;
        margin-bottom: 8px;
    }

    .upload-content p {
        font-size: 0.85rem;
        margin-bottom: 4px;
    }

    .upload-note {
        font-size: 0.75rem !important;
        margin-bottom: 15px !important;
    }

    .upload-btn {
        padding: 10px 20px;
        font-size: 0.9rem;
    }

    /* Option buttons mobile layout */
    .option-buttons {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 8px;
    }

    .option-btn {
        padding: 10px 8px;
        font-size: 0.8rem;
        min-width: auto;
    }

    .option-btn i {
        font-size: 1rem;
        margin-bottom: 4px;
    }

    /* Tourist guide modal mobile */
    .guide-search {
        flex-direction: column;
        gap: 8px;
    }

    .guide-search input {
        padding: 10px 12px;
        font-size: 16px; /* Prevents zoom on iOS */
    }

    .search-btn {
        padding: 10px 12px;
        font-size: 0.9rem;
    }

    .district-categories {
        gap: 6px;
        margin-bottom: 15px;
    }

    .category-btn {
        padding: 6px 12px;
        font-size: 0.8rem;
        border-radius: 15px;
    }

    .districts-grid {
        grid-template-columns: 1fr;
        gap: 12px;
        max-height: 50vh;
    }

    .district-card {
        padding: 15px;
    }

    .district-card h4 {
        font-size: 1rem;
        margin-bottom: 8px;
    }

    .district-card .region {
        padding: 3px 10px;
        font-size: 0.75rem;
        margin-bottom: 8px;
    }

    .district-card .attractions {
        font-size: 0.8rem;
        line-height: 1.3;
    }

    /* Itinerary modal mobile */
    .itinerary-form {
        gap: 15px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .destinations-selector {
        flex-direction: column;
        gap: 8px;
    }

    .destinations-selector input {
        padding: 10px 12px;
        font-size: 16px; /* Prevents zoom on iOS */
    }

    .add-destination-btn {
        padding: 10px 12px;
        font-size: 0.9rem;
        align-self: stretch;
    }

    .form-group select {
        padding: 10px 12px;
        font-size: 16px; /* Prevents zoom on iOS */
    }

    .interests-selector {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .interest-checkbox {
        padding: 10px 8px;
        background: var(--gray-50);
        border-radius: 8px;
    }

    .interest-checkbox input[type="checkbox"] {
        width: 16px;
        height: 16px;
    }

    /* Voice modal mobile */
    .voice-modal .modal-body {
        padding: 20px 15px;
    }

    .language-selector select {
        padding: 10px 12px;
        font-size: 16px; /* Prevents zoom on iOS */
        width: 100%;
    }

    .voice-status {
        padding: 25px 15px;
        margin-bottom: 20px;
    }

    .voice-status i {
        font-size: 2.5rem;
    }

    .voice-buttons {
        flex-direction: column;
        gap: 10px;
        margin-bottom: 20px;
    }

    .voice-record-btn,
    .voice-stop-btn {
        padding: 12px 20px;
        font-size: 0.9rem;
        width: 100%;
        justify-content: center;
    }

    .voice-transcript {
        padding: 15px;
    }

    .voice-transcript h4 {
        font-size: 0.9rem;
        margin-bottom: 8px;
    }

    .voice-transcript p {
        padding: 12px;
        font-size: 0.85rem;
        margin-bottom: 12px;
    }

    .send-transcript-btn {
        padding: 8px 16px;
        font-size: 0.9rem;
        width: 100%;
        justify-content: center;
    }
}

/* Touch-friendly improvements */
@media (hover: none) and (pointer: coarse) {
    .action-btn,
    .suggestion-card,
    .quick-reply,
    .heritage-btn,
    .forest-upload-btn,
    .voice-btn,
    .tree-id-btn,
    .guide-btn,
    .itinerary-btn {
        min-height: 44px; /* Apple's recommended touch target size */
    }

    .fab-main {
        min-width: 48px;
        min-height: 48px;
    }

    .fab-option {
        min-width: 40px;
        min-height: 40px;
    }

    /* Remove hover effects on touch devices */
    .action-btn:hover,
    .suggestion-card:hover,
    .quick-reply:hover,
    .heritage-btn:hover,
    .forest-upload-btn:hover,
    .voice-btn:hover,
    .tree-id-btn:hover,
    .guide-btn:hover,
    .itinerary-btn:hover {
        transform: none;
    }
}

/* iOS Safari specific fixes */
@supports (-webkit-touch-callout: none) {
    .app-container {
        height: -webkit-fill-available;
    }

    .chat-main {
        height: -webkit-fill-available;
    }

    .chat-messages-container {
        height: calc(-webkit-fill-available - 120px);
    }
}

/* Prevent zoom on input focus (iOS) */
input[type="text"],
input[type="email"],
input[type="password"],
textarea,
select {
    font-size: 16px !important;
}

/* Prevent pull-to-refresh */
body {
    overscroll-behavior: none;
    -webkit-overflow-scrolling: touch;
}

/* Hide address bar on mobile */
@media screen and (max-width: 768px) {
    .app-container {
        min-height: 100vh;
        min-height: -webkit-fill-available;
    }

    body {
        min-height: 100vh;
        min-height: -webkit-fill-available;
    }
}
