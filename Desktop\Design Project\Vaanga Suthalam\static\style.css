/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 50%, #ffcc02 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, #d32f2f 0%, #f57c00 100%);
    color: white;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 15px;
}

.logo i {
    font-size: 2.5rem;
    color: #ffcc02;
}

.logo h1 {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
}

.tagline {
    font-size: 0.9rem;
    opacity: 0.9;
    margin-top: 5px;
}

.header-info {
    display: flex;
    gap: 20px;
}

.info-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.info-item i {
    color: #ffcc02;
}

/* Welcome Section */
.welcome-section {
    padding: 30px;
    background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
    border-bottom: 2px solid #ff6b35;
}

.welcome-content h2 {
    color: #d32f2f;
    margin-bottom: 15px;
    font-size: 1.5rem;
}

.welcome-content p {
    margin-bottom: 20px;
    line-height: 1.6;
    color: #555;
}

.quick-topics {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.topic-tag {
    background: #ff6b35;
    color: white;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

/* Chat Messages */
.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    max-height: 400px;
}

.message {
    margin-bottom: 20px;
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.message.user {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.message.user .message-avatar {
    background: #2196f3;
    color: white;
}

.message.bot .message-avatar {
    background: #ff6b35;
    color: white;
}

.message-content {
    max-width: 70%;
    padding: 15px 20px;
    border-radius: 20px;
    line-height: 1.5;
    word-wrap: break-word;
}

.message.user .message-content {
    background: #2196f3;
    color: white;
    border-bottom-right-radius: 5px;
}

.message.bot .message-content {
    background: #f5f5f5;
    color: #333;
    border-bottom-left-radius: 5px;
    border: 1px solid #e0e0e0;
}

.message-time {
    font-size: 0.75rem;
    opacity: 0.7;
    margin-top: 5px;
}

/* Typing Indicator */
.typing-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px 20px;
    color: #666;
    font-style: italic;
}

.typing-dots {
    display: flex;
    gap: 4px;
}

.typing-dots span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #ff6b35;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% { transform: scale(0); opacity: 0.5; }
    40% { transform: scale(1); opacity: 1; }
}

/* Chat Input */
.chat-input-container {
    padding: 20px;
    background: white;
    border-top: 1px solid #e0e0e0;
}

.chat-input-wrapper {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}

#messageInput {
    flex: 1;
    padding: 15px 20px;
    border: 2px solid #e0e0e0;
    border-radius: 25px;
    font-size: 1rem;
    outline: none;
    transition: border-color 0.3s;
}

#messageInput:focus {
    border-color: #ff6b35;
}

#sendButton {
    width: 50px;
    height: 50px;
    border: none;
    background: #ff6b35;
    color: white;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
}

#sendButton:hover {
    background: #e55a2b;
    transform: scale(1.05);
}

#sendButton:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

.input-info {
    display: flex;
    justify-content: space-between;
    font-size: 0.8rem;
    color: #666;
}

/* Quick Suggestions */
.quick-suggestions {
    padding: 0 20px 20px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.suggestion-item {
    background: #fff3e0;
    border: 1px solid #ff6b35;
    color: #d32f2f;
    padding: 10px 15px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s;
    font-size: 0.9rem;
}

.suggestion-item:hover {
    background: #ff6b35;
    color: white;
    transform: translateY(-2px);
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    z-index: 1000;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid #ff6b35;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        margin: 0;
        border-radius: 0;
    }
    
    .header-content {
        flex-direction: column;
        text-align: center;
    }
    
    .logo h1 {
        font-size: 1.5rem;
    }
    
    .welcome-section {
        padding: 20px;
    }
    
    .message-content {
        max-width: 85%;
    }
    
    .quick-suggestions {
        justify-content: center;
    }
    
    .header-info {
        justify-content: center;
    }
}
