#!/usr/bin/env python3
"""
Startup script for Vaanga Suthalam that handles Windows console issues
"""

import os
import sys
import subprocess

def start_vaanga_suthalam():
    """Start the Vaanga Suthalam app with proper error handling"""
    
    print("🚀 Starting Vaanga Suthalam - Tamil Nadu AI Explorer")
    print("=" * 60)
    
    try:
        # Set environment variables to handle Windows console issues
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        os.environ['FLASK_ENV'] = 'production'
        
        # Import and run the app
        from app import app, local_model, model_loaded
        
        print(f"🤖 Local Model Status: {'✅ Loaded' if model_loaded else '⚠️  Fallback Mode'}")
        print("🌐 Server starting on: http://localhost:5000")
        print("📱 Open the URL in your browser to use the app")
        print("🛑 Press Ctrl+C to stop the server")
        print("-" * 60)
        
        # Run the app with error handling
        try:
            app.run(
                debug=False,
                host='127.0.0.1',  # Use localhost instead of 0.0.0.0
                port=5000,
                use_reloader=False,  # Disable reloader to avoid console issues
                threaded=True
            )
        except OSError as e:
            if "Windows error 6" in str(e) or "error 6" in str(e).lower():
                print("\n⚠️  Windows console encoding issue detected")
                print("✅ But your app is working fine!")
                print("🌐 Open: http://localhost:5000 in your browser")
                print("💡 The app is running in the background")
                
                # Keep the script running
                try:
                    while True:
                        import time
                        time.sleep(1)
                except KeyboardInterrupt:
                    print("\n🛑 Server stopped by user")
            else:
                raise e
                
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure you're in the correct directory")
        print("📁 Should be: Desktop\\Design Project\\Vaanga Suthalam")
        
    except Exception as e:
        print(f"❌ Error starting app: {e}")
        print("🔧 Try running: python app_simple.py")

def check_dependencies():
    """Check if required dependencies are available"""
    
    print("🔍 Checking dependencies...")
    
    try:
        import flask
        print("✅ Flask available")
    except ImportError:
        print("❌ Flask not found - install with: pip install flask")
        return False
    
    try:
        import flask_cors
        print("✅ Flask-CORS available")
    except ImportError:
        print("❌ Flask-CORS not found - install with: pip install flask-cors")
        return False
    
    try:
        from llama_cpp import Llama
        print("✅ llama-cpp-python available")
    except ImportError:
        print("⚠️  llama-cpp-python not available (will use fallback)")
    
    return True

def main():
    """Main function"""
    
    print("Vaanga Suthalam - Tamil Nadu AI Explorer")
    print("Local AI-Powered Tourism & Agriculture Guide")
    print()
    
    # Check dependencies
    if not check_dependencies():
        print("\n❌ Missing dependencies. Please install them first.")
        return
    
    print()
    
    # Start the app
    start_vaanga_suthalam()

if __name__ == "__main__":
    main()
