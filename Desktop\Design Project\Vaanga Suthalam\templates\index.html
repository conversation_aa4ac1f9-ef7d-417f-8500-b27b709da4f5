<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>Vaanga Suthalam - Tamil Nadu Explorer</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <meta name="theme-color" content="#FF6B35">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="Vaanga Suthalam">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="format-detection" content="telephone=no">
    <style>
        /* Immediate full-screen styles to prevent flash */
        html, body {
            margin: 0;
            padding: 0;
            height: 100vh;
            width: 100vw;
            overflow: hidden;
            position: fixed;
            top: 0;
            left: 0;
        }
    </style>
</head>
<body>
    <!-- Background Elements -->
    <div class="bg-pattern"></div>
    <div class="bg-gradient"></div>

    <div class="app-container">
        <!-- Sidebar -->
        <aside class="sidebar">
            <div class="sidebar-header">
                <div class="logo-container">
                    <div class="logo-icon">
                        <i class="fas fa-temple"></i>
                    </div>
                    <div class="logo-text">
                        <h1>Vaanga Suthalam</h1>
                        <p>வாங்க சுத்தலாம்</p>
                    </div>
                </div>
            </div>

            <div class="sidebar-content">
                <div class="travel-stats">
                    <div class="stat-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <div>
                            <span class="stat-number">32</span>
                            <span class="stat-label">Districts</span>
                        </div>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-temple"></i>
                        <div>
                            <span class="stat-number">38,000+</span>
                            <span class="stat-label">Temples</span>
                        </div>
                    </div>
                    <div class="stat-item">
                        <i class="fas fa-utensils"></i>
                        <div>
                            <span class="stat-number">100+</span>
                            <span class="stat-label">Dishes</span>
                        </div>
                    </div>
                </div>

                <div class="quick-actions">
                    <h3>Popular Topics</h3>
                    <div class="action-buttons">
                        <button class="action-btn" data-topic="temples">
                            <i class="fas fa-temple"></i>
                            <span>Temples</span>
                        </button>
                        <button class="action-btn" data-topic="food">
                            <i class="fas fa-bowl-food"></i>
                            <span>Cuisine</span>
                        </button>
                        <button class="action-btn" data-topic="transport">
                            <i class="fas fa-train"></i>
                            <span>Transport</span>
                        </button>
                        <button class="action-btn" data-topic="culture">
                            <i class="fas fa-masks-theater"></i>
                            <span>Culture</span>
                        </button>
                        <button class="action-btn" data-topic="places">
                            <i class="fas fa-mountain"></i>
                            <span>Places</span>
                        </button>
                        <button class="action-btn" data-topic="shopping">
                            <i class="fas fa-shopping-bag"></i>
                            <span>Shopping</span>
                        </button>
                        <button class="action-btn" data-topic="agriculture">
                            <i class="fas fa-seedling"></i>
                            <span>Agriculture</span>
                        </button>
                        <button class="action-btn" data-topic="crops">
                            <i class="fas fa-wheat-awn"></i>
                            <span>Crops</span>
                        </button>
                        <button class="action-btn" data-topic="heritage">
                            <i class="fas fa-landmark"></i>
                            <span>Heritage</span>
                        </button>
                        <button class="action-btn" data-topic="forest">
                            <i class="fas fa-tree"></i>
                            <span>Forest</span>
                        </button>
                        <button class="action-btn" data-topic="trees">
                            <i class="fas fa-leaf"></i>
                            <span>Tree ID</span>
                        </button>
                        <button class="action-btn" data-topic="districts">
                            <i class="fas fa-map"></i>
                            <span>Districts</span>
                        </button>
                        <button class="action-btn" data-topic="itinerary">
                            <i class="fas fa-route"></i>
                            <span>Itinerary</span>
                        </button>
                    </div>
                </div>

                <!-- Tree & Plant Identification -->
                <div class="tree-identification">
                    <h3>🌳 Tree & Plant ID</h3>
                    <p>Identify any tree, plant, or leaf with AI</p>
                    <button class="tree-id-btn" id="openTreeIdentification">
                        <i class="fas fa-search"></i>
                        <span>Identify Tree/Plant</span>
                    </button>
                </div>

                <!-- Tourist Guide -->
                <div class="tourist-guide">
                    <h3>🗺️ Complete TN Guide</h3>
                    <p>Explore all 38 districts of Tamil Nadu</p>
                    <button class="guide-btn" id="openTouristGuide">
                        <i class="fas fa-compass"></i>
                        <span>Explore Districts</span>
                    </button>
                </div>

                <!-- Itinerary Planner -->
                <div class="itinerary-planner">
                    <h3>📋 Trip Planner</h3>
                    <p>Create custom travel itineraries</p>
                    <button class="itinerary-btn" id="openItineraryPlanner">
                        <i class="fas fa-calendar-alt"></i>
                        <span>Plan Your Trip</span>
                    </button>
                </div>
            </div>
        </aside>

        <!-- Main Chat Area -->
        <main class="chat-main">
            <!-- Chat Header -->
            <div class="chat-header">
                <div class="chat-header-info">
                    <div class="bot-avatar">
                        <i class="fas fa-robot"></i>
                        <div class="status-indicator"></div>
                    </div>
                    <div class="bot-info">
                        <h2>Tamil Nadu Travel Assistant</h2>
                        <p class="bot-status">
                            <i class="fas fa-circle"></i>
                            Online - Ready to help you explore Tamil Nadu
                        </p>
                    </div>
                </div>
                <div class="chat-actions">
                    <button class="action-icon" id="clearChat" title="Clear Chat">
                        <i class="fas fa-trash"></i>
                    </button>
                    <button class="action-icon" id="toggleSidebar" title="Toggle Sidebar">
                        <i class="fas fa-bars"></i>
                    </button>
                </div>
            </div>

            <!-- Welcome Card -->
            <div class="welcome-card" id="welcomeCard">
                <div class="welcome-content">
                    <div class="welcome-icon">
                        <i class="fas fa-hands-praying"></i>
                    </div>
                    <h2>வணக்கம்! Welcome to Tamil Nadu!</h2>
                    <p>I'm your AI travel companion, ready to guide you through the incredible journey of exploring Tamil Nadu. From ancient temples to delicious cuisine, I'm here to help!</p>

                    <div class="welcome-suggestions">
                        <div class="suggestion-card" data-message="What are the must-visit temples in Tamil Nadu?">
                            <i class="fas fa-temple"></i>
                            <span>Must-visit Temples</span>
                        </div>
                        <div class="suggestion-card" data-message="What are the famous foods I should try in Tamil Nadu?">
                            <i class="fas fa-bowl-food"></i>
                            <span>Local Cuisine</span>
                        </div>
                        <div class="suggestion-card" data-message="How do I travel between cities in Tamil Nadu?">
                            <i class="fas fa-train"></i>
                            <span>Transportation</span>
                        </div>
                        <div class="suggestion-card" data-message="What's the best time to visit Tamil Nadu?">
                            <i class="fas fa-calendar"></i>
                            <span>Best Time to Visit</span>
                        </div>
                        <div class="suggestion-card" data-message="Tell me about Tamil Nadu's agriculture and major crops">
                            <i class="fas fa-seedling"></i>
                            <span>Agriculture & Crops</span>
                        </div>
                        <div class="suggestion-card" data-message="What are the famous agricultural regions in Tamil Nadu?">
                            <i class="fas fa-tractor"></i>
                            <span>Farming Regions</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Chat Messages Container -->
            <div class="chat-messages-container">
                <div class="chat-messages" id="chatMessages">
                    <!-- Messages will be dynamically added here -->
                </div>

                <!-- Typing Indicator -->
                <div class="typing-indicator" id="typingIndicator" style="display: none;">
                    <div class="typing-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="typing-content">
                        <div class="typing-dots">
                            <span></span>
                            <span></span>
                            <span></span>
                        </div>
                        <span class="typing-text">Assistant is thinking...</span>
                    </div>
                </div>
            </div>

            <!-- Chat Input Area -->
            <div class="chat-input-area">
                <!-- Image Preview Area -->
                <div class="image-preview-container" id="imagePreviewContainer" style="display: none;">
                    <div class="image-preview">
                        <img id="previewImage" src="" alt="Preview">
                        <div class="image-info">
                            <span class="image-name" id="imageName"></span>
                            <button class="remove-image" id="removeImage">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <div class="input-container">
                    <div class="input-wrapper">
                        <button class="attachment-btn" id="attachmentBtn" title="Upload Image">
                            <i class="fas fa-camera"></i>
                        </button>
                        <textarea id="messageInput" placeholder="Ask me about Tamil Nadu travel or agriculture..." maxlength="500" rows="1"></textarea>
                        <div class="input-actions">
                            <span class="char-count" id="charCount">0/500</span>
                            <button id="sendButton" type="button" disabled>
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>

                    <!-- Hidden file input -->
                    <input type="file" id="imageInput" accept="image/*" style="display: none;">
                </div>

                <!-- Quick Reply Suggestions -->
                <div class="quick-replies" id="quickReplies">
                    <div class="quick-reply" data-message="Tell me about Chennai">Chennai</div>
                    <div class="quick-reply" data-message="Best temples to visit">Temples</div>
                    <div class="quick-reply" data-message="Tamil food recommendations">Food</div>
                    <div class="quick-reply" data-message="How to get around">Transport</div>
                    <div class="quick-reply" data-message="Tamil Nadu agriculture">Agriculture</div>
                    <div class="quick-reply" data-message="Major crops grown">Crops</div>
                </div>
            </div>
        </main>
    </div>

    <!-- Image Upload Modal -->
    <div class="modal-overlay" id="imageUploadModal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>🔬 Plant & Tree Analysis</h3>
                <button class="modal-close" id="closeImageModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="upload-area" id="uploadArea">
                    <div class="upload-content">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <h4>Upload Plant/Crop Image</h4>
                        <p>Drag and drop an image here, or click to select</p>
                        <p class="upload-note">Supported formats: JPG, PNG, GIF, WebP (Max 10MB)</p>
                        <input type="file" id="modalImageInput" accept="image/*" style="display: none;">
                        <button class="upload-btn" id="selectImageBtn">
                            <i class="fas fa-folder-open"></i>
                            Select Image
                        </button>
                    </div>
                </div>

                <div class="analysis-options">
                    <h4>🔬 Choose Analysis Type</h4>
                    <div class="option-buttons">
                        <button class="option-btn active" data-type="tree_identification">
                            <i class="fas fa-search"></i>
                            <span>Tree/Plant ID</span>
                        </button>
                        <button class="option-btn" data-type="medicinal">
                            <i class="fas fa-mortar-pestle"></i>
                            <span>Medicinal Analysis</span>
                        </button>
                        <button class="option-btn" data-type="forest">
                            <i class="fas fa-tree"></i>
                            <span>Forest Ecosystem</span>
                        </button>
                        <button class="option-btn" data-type="full">
                            <i class="fas fa-seedling"></i>
                            <span>Agricultural Analysis</span>
                        </button>
                        <button class="option-btn" data-type="disease">
                            <i class="fas fa-bug"></i>
                            <span>Disease Detection</span>
                        </button>
                        <button class="option-btn" data-type="nutrition">
                            <i class="fas fa-leaf"></i>
                            <span>Nutrition Status</span>
                        </button>
                    </div>
                    <div class="analysis-description">
                        <p id="analysisDescription">🌳 Comprehensive plant identification with scientific names, Tamil names, characteristics, uses, and cultivation information.</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" id="cancelUpload">Cancel</button>
                <button class="btn-primary" id="analyzeImage" disabled>
                    <i class="fas fa-search"></i>
                    Analyze Image
                </button>
            </div>
        </div>
    </div>

    <!-- Tourist Guide Modal -->
    <div class="modal-overlay" id="touristGuideModal" style="display: none;">
        <div class="modal-content guide-modal">
            <div class="modal-header">
                <h3>🗺️ Tamil Nadu Complete Guide</h3>
                <button class="modal-close" id="closeTouristGuideModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="guide-search">
                    <input type="text" id="districtSearch" placeholder="Search districts, cities, or attractions...">
                    <button class="search-btn" id="searchDistricts">
                        <i class="fas fa-search"></i>
                    </button>
                </div>

                <div class="district-categories">
                    <button class="category-btn active" data-region="all">All Districts</button>
                    <button class="category-btn" data-region="Northern">Northern</button>
                    <button class="category-btn" data-region="Western">Western</button>
                    <button class="category-btn" data-region="Central">Central</button>
                    <button class="category-btn" data-region="Southern">Southern</button>
                    <button class="category-btn" data-region="Eastern">Eastern</button>
                </div>

                <div class="districts-grid" id="districtsGrid">
                    <!-- Districts will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Itinerary Planner Modal -->
    <div class="modal-overlay" id="itineraryModal" style="display: none;">
        <div class="modal-content itinerary-modal">
            <div class="modal-header">
                <h3>📋 Trip Itinerary Planner</h3>
                <button class="modal-close" id="closeItineraryModal">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="itinerary-form">
                    <div class="form-group">
                        <label>Select Destinations:</label>
                        <div class="destinations-selector">
                            <input type="text" id="destinationInput" placeholder="Add destinations (e.g., Chennai, Madurai)">
                            <button class="add-destination-btn" id="addDestination">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                        <div class="selected-destinations" id="selectedDestinations">
                            <!-- Selected destinations will appear here -->
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label>Duration (days):</label>
                            <select id="tripDuration">
                                <option value="3">3 Days</option>
                                <option value="5">5 Days</option>
                                <option value="7" selected>7 Days</option>
                                <option value="10">10 Days</option>
                                <option value="14">14 Days</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label>Budget Level:</label>
                            <select id="budgetLevel">
                                <option value="low">Budget (₹1000-2000/day)</option>
                                <option value="medium" selected>Mid-range (₹2000-5000/day)</option>
                                <option value="high">Luxury (₹5000+/day)</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label>Interests:</label>
                        <div class="interests-selector">
                            <label class="interest-checkbox">
                                <input type="checkbox" value="temples"> Temples & Heritage
                            </label>
                            <label class="interest-checkbox">
                                <input type="checkbox" value="nature"> Nature & Wildlife
                            </label>
                            <label class="interest-checkbox">
                                <input type="checkbox" value="culture"> Culture & Arts
                            </label>
                            <label class="interest-checkbox">
                                <input type="checkbox" value="food"> Food & Cuisine
                            </label>
                            <label class="interest-checkbox">
                                <input type="checkbox" value="beaches"> Beaches
                            </label>
                            <label class="interest-checkbox">
                                <input type="checkbox" value="hills"> Hill Stations
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" id="cancelItinerary">Cancel</button>
                <button class="btn-primary" id="createItinerary">
                    <i class="fas fa-route"></i>
                    Create Itinerary
                </button>
            </div>
        </div>
    </div>

    <!-- Floating Action Menu -->
    <div class="floating-menu" id="floatingMenu">
        <button class="fab-main" id="fabMain">
            <i class="fas fa-compass"></i>
        </button>
        <div class="fab-options">
            <button class="fab-option" data-action="weather" title="Weather Info">
                <i class="fas fa-cloud-sun"></i>
            </button>
            <button class="fab-option" data-action="emergency" title="Emergency Contacts">
                <i class="fas fa-phone"></i>
            </button>
            <button class="fab-option" data-action="translate" title="Basic Tamil Phrases">
                <i class="fas fa-language"></i>
            </button>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay" style="display: none;">
        <div class="loading-content">
            <div class="loading-spinner"></div>
            <h3>Connecting to your travel guide...</h3>
            <p>Please wait while we prepare your Tamil Nadu experience</p>
        </div>
    </div>

    <script src="{{ url_for('static', filename='script.js') }}"></script>
</body>
</html>
