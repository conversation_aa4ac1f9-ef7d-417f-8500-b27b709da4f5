<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vaanga Suthalam - Troubleshooting Guide</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #FF6B35;
            text-align: center;
            margin-bottom: 30px;
        }
        .error-box {
            background: #ffebee;
            border: 2px solid #f44336;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .solution-box {
            background: #e8f5e8;
            border: 2px solid #4caf50;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .code-block {
            background: #f5f5f5;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            overflow-x: auto;
        }
        .step {
            background: #f0f8ff;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 15px 0;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffc107;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .success {
            background: #d4edda;
            border: 1px solid #28a745;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Vaanga Suthalam - Troubleshooting Guide</h1>
        
        <div class="error-box">
            <h2>❌ Common Error</h2>
            <p><strong>"Sorry, I encountered an error. Please check your internet connection and try again."</strong></p>
            <p>This error typically occurs when:</p>
            <ul>
                <li>The Flask backend is not running</li>
                <li>Python dependencies are missing</li>
                <li>Local model setup is incomplete</li>
                <li>Port conflicts or firewall issues</li>
            </ul>
        </div>

        <div class="solution-box">
            <h2>✅ Quick Fix Solutions</h2>
            
            <div class="step">
                <h3>Step 1: Install Required Dependencies</h3>
                <p>Open Command Prompt/PowerShell and run:</p>
                <div class="code-block">
pip install flask flask-cors pillow python-dotenv
                </div>
            </div>

            <div class="step">
                <h3>Step 2: Use the Simplified App</h3>
                <p>I've created a working version that doesn't require the local model:</p>
                <div class="code-block">
python app_simple.py
                </div>
                <p>This version provides intelligent responses without needing llama-cpp-python.</p>
            </div>

            <div class="step">
                <h3>Step 3: Check if App is Running</h3>
                <p>You should see output like:</p>
                <div class="code-block">
🚀 Starting Vaanga Suthalam - Tamil Nadu Guide
🔄 Running with intelligent fallback responses  
🌐 Open: http://localhost:5000
✅ All features available with contextual responses
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
                </div>
            </div>

            <div class="step">
                <h3>Step 4: Test the Application</h3>
                <p>Open your browser and go to:</p>
                <div class="code-block">
http://localhost:5000
                </div>
                <p>Try these features:</p>
                <ul>
                    <li>💬 Chat about Tamil Nadu tourism</li>
                    <li>🌱 Upload plant images for analysis</li>
                    <li>🗺️ Ask about districts and travel</li>
                    <li>🍛 Inquire about food and culture</li>
                </ul>
            </div>
        </div>

        <div class="warning">
            <h3>⚠️ Local Model Setup (Optional)</h3>
            <p>For the full AI experience with local models:</p>
            <ol>
                <li><strong>Install Visual Studio Build Tools</strong> (for Windows)</li>
                <li><strong>Install llama-cpp-python:</strong>
                    <div class="code-block">pip install llama-cpp-python</div>
                </li>
                <li><strong>Ensure model file exists:</strong>
                    <div class="code-block">models/bartowski/Llama-3.2-3B-Instruct-GGUF/Llama-3.2-3B-Instruct-Q4_K_S.gguf</div>
                </li>
                <li><strong>Run the full app:</strong>
                    <div class="code-block">python app.py</div>
                </li>
            </ol>
        </div>

        <div class="success">
            <h3>🎉 What Works Right Now</h3>
            <p>The simplified app provides:</p>
            <ul>
                <li>✅ <strong>Intelligent Chat</strong> - Contextual responses about Tamil Nadu</li>
                <li>✅ <strong>Plant Analysis</strong> - Botanical information and traditional uses</li>
                <li>✅ <strong>Tourism Guide</strong> - Comprehensive travel information</li>
                <li>✅ <strong>Cultural Insights</strong> - Food, festivals, and traditions</li>
                <li>✅ <strong>Agricultural Advice</strong> - Farming and crop guidance</li>
                <li>✅ <strong>Itinerary Planning</strong> - Custom travel plans</li>
            </ul>
        </div>

        <div class="step">
            <h3>🔍 Debugging Steps</h3>
            <p>If you still have issues:</p>
            <ol>
                <li><strong>Check Python version:</strong>
                    <div class="code-block">python --version</div>
                    <p>Should be Python 3.8 or higher</p>
                </li>
                <li><strong>Check if Flask is installed:</strong>
                    <div class="code-block">python -c "import flask; print('Flask OK')"</div>
                </li>
                <li><strong>Check port availability:</strong>
                    <div class="code-block">netstat -an | findstr :5000</div>
                </li>
                <li><strong>Try different port:</strong>
                    <p>Edit app_simple.py and change port to 8000 or 3000</p>
                </li>
            </ol>
        </div>

        <div class="success">
            <h3>📞 Support</h3>
            <p>If you need further assistance:</p>
            <ul>
                <li>Check the console output for specific error messages</li>
                <li>Ensure all files are in the correct directory</li>
                <li>Try running as administrator if permission issues occur</li>
                <li>Check Windows Defender/Firewall settings</li>
            </ul>
        </div>
    </div>
</body>
</html>
