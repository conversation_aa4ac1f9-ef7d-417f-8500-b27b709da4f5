#!/usr/bin/env python3
"""
Test if the app is working with the local model
"""

import requests
import time
import threading
import sys
import os

def test_app_endpoints():
    """Test if the app endpoints are working"""
    
    base_url = "http://localhost:5000"
    
    print("🧪 Testing Vaanga Suthalam Endpoints")
    print("=" * 50)
    
    # Wait a moment for server to start
    time.sleep(2)
    
    try:
        # Test 1: Health check
        print("1. Testing health endpoint...")
        try:
            response = requests.get(f"{base_url}/health", timeout=5)
            if response.status_code == 200:
                print("✅ Health check passed")
                data = response.json()
                print(f"   Status: {data.get('status', 'unknown')}")
            else:
                print(f"⚠️  Health check returned: {response.status_code}")
        except requests.exceptions.RequestException:
            print("❌ Health endpoint not responding")
            return False
        
        # Test 2: Chat endpoint
        print("\n2. Testing chat endpoint...")
        try:
            chat_data = {"message": "Tell me about Chennai"}
            response = requests.post(f"{base_url}/chat", json=chat_data, timeout=10)
            if response.status_code == 200:
                print("✅ Chat endpoint working")
                data = response.json()
                if 'response' in data:
                    print(f"   Response preview: {data['response'][:100]}...")
                    if 'Chennai' in data['response'] or 'Tamil Nadu' in data['response']:
                        print("✅ Response contains relevant content")
                    else:
                        print("⚠️  Response may not be contextually relevant")
                else:
                    print("⚠️  Response format unexpected")
            else:
                print(f"❌ Chat endpoint error: {response.status_code}")
                print(f"   Response: {response.text}")
        except requests.exceptions.RequestException as e:
            print(f"❌ Chat endpoint failed: {e}")
        
        # Test 3: Home page
        print("\n3. Testing home page...")
        try:
            response = requests.get(base_url, timeout=5)
            if response.status_code == 200:
                print("✅ Home page loading")
                if 'Vaanga Suthalam' in response.text:
                    print("✅ Page content looks correct")
                else:
                    print("⚠️  Page content may be incorrect")
            else:
                print(f"❌ Home page error: {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"❌ Home page failed: {e}")
        
        print("\n🎉 Testing completed!")
        print("🌐 Your app should be working at: http://localhost:5000")
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        return False

def start_app_in_background():
    """Start the app in a separate thread"""
    
    try:
        # Import and start the app
        from app import app
        app.run(debug=False, host='127.0.0.1', port=5000, use_reloader=False, threaded=True)
    except Exception as e:
        print(f"Error starting app: {e}")

def main():
    """Main test function"""
    
    print("🚀 Vaanga Suthalam - Local Model Test")
    print("This will start the app and test if it's working")
    print()
    
    # Check if app is already running
    try:
        response = requests.get("http://localhost:5000/health", timeout=2)
        if response.status_code == 200:
            print("✅ App is already running!")
            test_app_endpoints()
            return
    except:
        pass
    
    print("🔄 Starting app in background...")
    
    # Start app in background thread
    app_thread = threading.Thread(target=start_app_in_background, daemon=True)
    app_thread.start()
    
    # Wait for app to start
    print("⏳ Waiting for app to start...")
    for i in range(10):
        try:
            response = requests.get("http://localhost:5000/health", timeout=1)
            if response.status_code == 200:
                print("✅ App started successfully!")
                break
        except:
            pass
        time.sleep(1)
        print(f"   Waiting... ({i+1}/10)")
    else:
        print("❌ App failed to start within 10 seconds")
        return
    
    # Run tests
    test_app_endpoints()
    
    print("\n📋 Next Steps:")
    print("1. Open your browser")
    print("2. Go to: http://localhost:5000")
    print("3. Try chatting about Tamil Nadu")
    print("4. Upload plant images for analysis")
    print("5. Press Ctrl+C here to stop the test")
    
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 Test stopped by user")

if __name__ == "__main__":
    main()
