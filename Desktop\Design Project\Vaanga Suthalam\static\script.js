// DOM Elements
const messageInput = document.getElementById('messageInput');
const sendButton = document.getElementById('sendButton');
const chatMessages = document.getElementById('chatMessages');
const typingIndicator = document.getElementById('typingIndicator');
const charCount = document.getElementById('charCount');
const welcomeCard = document.getElementById('welcomeCard');
const loadingOverlay = document.getElementById('loadingOverlay');
const clearChatBtn = document.getElementById('clearChat');
const toggleSidebarBtn = document.getElementById('toggleSidebar');
const fabMain = document.getElementById('fabMain');
const floatingMenu = document.getElementById('floatingMenu');
const quickReplies = document.getElementById('quickReplies');

// Image upload elements
const attachmentBtn = document.getElementById('attachmentBtn');
const imageInput = document.getElementById('imageInput');
const imagePreviewContainer = document.getElementById('imagePreviewContainer');
const previewImage = document.getElementById('previewImage');
const imageName = document.getElementById('imageName');
const removeImageBtn = document.getElementById('removeImage');

// Modal elements
const imageUploadModal = document.getElementById('imageUploadModal');
const openImageUploadBtn = document.getElementById('openImageUpload');
const closeImageModalBtn = document.getElementById('closeImageModal');
const modalImageInput = document.getElementById('modalImageInput');
const selectImageBtn = document.getElementById('selectImageBtn');
const uploadArea = document.getElementById('uploadArea');
const analyzeImageBtn = document.getElementById('analyzeImage');
const cancelUploadBtn = document.getElementById('cancelUpload');

// New feature elements
const openTreeIdentificationBtn = document.getElementById('openTreeIdentification');
const openTouristGuideBtn = document.getElementById('openTouristGuide');
const openItineraryPlannerBtn = document.getElementById('openItineraryPlanner');
const touristGuideModal = document.getElementById('touristGuideModal');
const closeTouristGuideModalBtn = document.getElementById('closeTouristGuideModal');
const itineraryModal = document.getElementById('itineraryModal');
const closeItineraryModalBtn = document.getElementById('closeItineraryModal');
const districtSearch = document.getElementById('districtSearch');
const searchDistrictsBtn = document.getElementById('searchDistricts');
const districtsGrid = document.getElementById('districtsGrid');
const destinationInput = document.getElementById('destinationInput');
const addDestinationBtn = document.getElementById('addDestination');
const selectedDestinations = document.getElementById('selectedDestinations');
const createItineraryBtn = document.getElementById('createItinerary');
const cancelItineraryBtn = document.getElementById('cancelItinerary');

// State
let isTyping = false;
let messageHistory = [];
let sidebarOpen = false;
let selectedImage = null;
let analysisType = 'full';
let selectedDestinationsList = [];
let currentDistricts = [];

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    addWelcomeMessage();
    autoResizeTextarea();
    messageInput.focus();
});

// Event Listeners
function initializeEventListeners() {
    // Send button click
    sendButton.addEventListener('click', handleSendMessage);

    // Enter key press (Ctrl/Cmd + Enter to send, Enter for new line)
    messageInput.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
            e.preventDefault();
            handleSendMessage();
        }
    });

    // Character count and auto-resize
    messageInput.addEventListener('input', function() {
        updateCharCount();
        autoResizeTextarea();
        updateSendButton();
    });

    // Welcome card suggestions
    if (welcomeCard) {
        welcomeCard.addEventListener('click', function(e) {
            if (e.target.closest('.suggestion-card')) {
                const message = e.target.closest('.suggestion-card').getAttribute('data-message');
                messageInput.value = message;
                handleSendMessage();
            }
        });
    }

    // Quick replies
    if (quickReplies) {
        quickReplies.addEventListener('click', function(e) {
            if (e.target.classList.contains('quick-reply')) {
                const message = e.target.getAttribute('data-message');
                messageInput.value = message;
                handleSendMessage();
            }
        });
    }

    // Sidebar action buttons
    document.addEventListener('click', function(e) {
        if (e.target.closest('.action-btn')) {
            const topic = e.target.closest('.action-btn').getAttribute('data-topic');
            handleTopicClick(topic);
        }
    });

    // Clear chat
    if (clearChatBtn) {
        clearChatBtn.addEventListener('click', clearChat);
    }

    // Toggle sidebar (mobile)
    if (toggleSidebarBtn) {
        toggleSidebarBtn.addEventListener('click', toggleSidebar);
    }

    // Floating action button
    if (fabMain) {
        fabMain.addEventListener('click', toggleFloatingMenu);
    }

    // FAB options
    document.addEventListener('click', function(e) {
        if (e.target.closest('.fab-option')) {
            const action = e.target.closest('.fab-option').getAttribute('data-action');
            handleFabAction(action);
        }
    });

    // Close floating menu when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.floating-menu')) {
            closeFloatingMenu();
        }
    });

    // Mobile sidebar overlay
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('sidebar-overlay')) {
            closeSidebar();
        }
    });

    // Image upload event listeners
    initializeImageUpload();

    // New features event listeners
    initializeNewFeatures();
}

// Initialize image upload functionality
function initializeImageUpload() {
    // Attachment button click
    if (attachmentBtn) {
        attachmentBtn.addEventListener('click', function() {
            imageInput.click();
        });
    }

    // Image input change
    if (imageInput) {
        imageInput.addEventListener('change', handleImageSelect);
    }

    // Remove image button
    if (removeImageBtn) {
        removeImageBtn.addEventListener('click', removeSelectedImage);
    }

    // Open image upload modal
    if (openImageUploadBtn) {
        openImageUploadBtn.addEventListener('click', openImageModal);
    }

    // Close modal
    if (closeImageModalBtn) {
        closeImageModalBtn.addEventListener('click', closeImageModal);
    }

    if (cancelUploadBtn) {
        cancelUploadBtn.addEventListener('click', closeImageModal);
    }

    // Modal image input
    if (modalImageInput) {
        modalImageInput.addEventListener('change', handleModalImageSelect);
    }

    // Select image button in modal
    if (selectImageBtn) {
        selectImageBtn.addEventListener('click', function() {
            modalImageInput.click();
        });
    }

    // Analyze image button
    if (analyzeImageBtn) {
        analyzeImageBtn.addEventListener('click', handleImageAnalysis);
    }

    // Analysis type buttons
    document.addEventListener('click', function(e) {
        if (e.target.closest('.option-btn')) {
            const btn = e.target.closest('.option-btn');
            document.querySelectorAll('.option-btn').forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
            analysisType = btn.getAttribute('data-type');
        }
    });

    // Drag and drop functionality
    if (uploadArea) {
        uploadArea.addEventListener('dragover', handleDragOver);
        uploadArea.addEventListener('dragleave', handleDragLeave);
        uploadArea.addEventListener('drop', handleDrop);
    }

    // Close modal when clicking outside
    if (imageUploadModal) {
        imageUploadModal.addEventListener('click', function(e) {
            if (e.target === imageUploadModal) {
                closeImageModal();
            }
        });
    }
}

// Add welcome message
function addWelcomeMessage() {
    const welcomeText = "வணக்கம்! (Vanakkam!) Welcome to Tamil Nadu! I'm your AI travel companion, ready to help you explore this incredible state. Feel free to ask me anything about temples, food, culture, transportation, or any other aspect of Tamil Nadu travel!";
    addMessage(welcomeText, 'bot');
}

// Handle topic clicks from sidebar
function handleTopicClick(topic) {
    const topicMessages = {
        temples: "Tell me about the most famous temples in Tamil Nadu and their significance",
        food: "What are the must-try Tamil Nadu dishes and where can I find authentic local food?",
        transport: "How do I travel around Tamil Nadu? What are the best transportation options?",
        culture: "Tell me about Tamil Nadu's culture, traditions, and festivals",
        places: "What are the top tourist destinations and hidden gems in Tamil Nadu?",
        shopping: "Where can I shop for traditional Tamil items like sarees, handicrafts, and souvenirs?",
        agriculture: "Tell me about Tamil Nadu's agriculture, major crops, and farming practices",
        crops: "What are the main crops grown in Tamil Nadu and their growing seasons?",
        heritage: "Show me the most important heritage sites and monuments in Tamil Nadu",
        forest: "Tell me about the medicinal plants and forest biodiversity in Tamil Nadu"
    };

    if (topic === 'trees') {
        analysisType = 'tree_identification';
        openImageModal();
    } else if (topic === 'districts') {
        openTouristGuideModal();
    } else if (topic === 'itinerary') {
        openItineraryModal();
    } else if (topicMessages[topic]) {
        messageInput.value = topicMessages[topic];
        handleSendMessage();
    }
}

// Image handling functions
function handleImageSelect(event) {
    const file = event.target.files[0];
    if (file) {
        displayImagePreview(file);
    }
}

function handleModalImageSelect(event) {
    const file = event.target.files[0];
    if (file) {
        selectedImage = file;
        analyzeImageBtn.disabled = false;

        // Update upload area to show selected file
        const uploadContent = uploadArea.querySelector('.upload-content');
        uploadContent.innerHTML = `
            <i class="fas fa-check-circle" style="color: #4CAF50;"></i>
            <h4>Image Selected</h4>
            <p>${file.name}</p>
            <p class="upload-note">Ready for analysis</p>
        `;
    }
}

function displayImagePreview(file) {
    const reader = new FileReader();
    reader.onload = function(e) {
        previewImage.src = e.target.result;
        imageName.textContent = file.name;
        imagePreviewContainer.style.display = 'block';
        selectedImage = file;

        // Update attachment button to show image is selected
        attachmentBtn.innerHTML = '<i class="fas fa-check"></i>';
        attachmentBtn.style.background = '#4CAF50';
    };
    reader.readAsDataURL(file);
}

function removeSelectedImage() {
    selectedImage = null;
    imagePreviewContainer.style.display = 'none';
    imageInput.value = '';

    // Reset attachment button
    attachmentBtn.innerHTML = '<i class="fas fa-camera"></i>';
    attachmentBtn.style.background = '';
}

function openImageModal() {
    imageUploadModal.style.display = 'flex';
    document.body.style.overflow = 'hidden';
}

function closeImageModal() {
    imageUploadModal.style.display = 'none';
    document.body.style.overflow = '';

    // Reset modal state
    selectedImage = null;
    analyzeImageBtn.disabled = true;
    modalImageInput.value = '';

    // Reset upload area
    const uploadContent = uploadArea.querySelector('.upload-content');
    uploadContent.innerHTML = `
        <i class="fas fa-cloud-upload-alt"></i>
        <h4>Upload Plant/Crop Image</h4>
        <p>Drag and drop an image here, or click to select</p>
        <p class="upload-note">Supported formats: JPG, PNG, GIF, WebP (Max 10MB)</p>
        <button class="upload-btn" id="selectImageBtn">
            <i class="fas fa-folder-open"></i>
            Select Image
        </button>
    `;

    // Re-attach event listener
    const newSelectBtn = uploadContent.querySelector('#selectImageBtn');
    if (newSelectBtn) {
        newSelectBtn.addEventListener('click', function() {
            modalImageInput.click();
        });
    }
}

// Handle floating action button actions
function handleFabAction(action) {
    const fabMessages = {
        weather: "What's the current weather like in Tamil Nadu and what's the best time to visit?",
        emergency: "What are the important emergency contact numbers and safety tips for tourists in Tamil Nadu?",
        translate: "Can you teach me some basic Tamil phrases that would be helpful for tourists?"
    };

    if (fabMessages[action]) {
        messageInput.value = fabMessages[action];
        handleSendMessage();
    }
    closeFloatingMenu();
}

// Toggle floating menu
function toggleFloatingMenu() {
    floatingMenu.classList.toggle('active');
}

// Close floating menu
function closeFloatingMenu() {
    floatingMenu.classList.remove('active');
}

// Toggle sidebar (mobile)
function toggleSidebar() {
    const sidebar = document.querySelector('.sidebar');
    const overlay = document.querySelector('.sidebar-overlay') || createSidebarOverlay();

    sidebarOpen = !sidebarOpen;
    sidebar.classList.toggle('active', sidebarOpen);
    overlay.classList.toggle('active', sidebarOpen);
}

// Close sidebar
function closeSidebar() {
    const sidebar = document.querySelector('.sidebar');
    const overlay = document.querySelector('.sidebar-overlay');

    sidebarOpen = false;
    sidebar.classList.remove('active');
    if (overlay) overlay.classList.remove('active');
}

// Create sidebar overlay for mobile
function createSidebarOverlay() {
    const overlay = document.createElement('div');
    overlay.className = 'sidebar-overlay';
    document.body.appendChild(overlay);
    return overlay;
}

// Clear chat
function clearChat() {
    if (confirm('Are you sure you want to clear the chat history?')) {
        chatMessages.innerHTML = '';
        messageHistory = [];
        if (welcomeCard) welcomeCard.style.display = 'block';
        if (quickReplies) quickReplies.style.display = 'flex';
        addWelcomeMessage();
    }
}

// Drag and drop handlers
function handleDragOver(e) {
    e.preventDefault();
    uploadArea.classList.add('dragover');
}

function handleDragLeave(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');
}

function handleDrop(e) {
    e.preventDefault();
    uploadArea.classList.remove('dragover');

    const files = e.dataTransfer.files;
    if (files.length > 0) {
        const file = files[0];
        if (file.type.startsWith('image/')) {
            selectedImage = file;
            analyzeImageBtn.disabled = false;

            // Update upload area
            const uploadContent = uploadArea.querySelector('.upload-content');
            uploadContent.innerHTML = `
                <i class="fas fa-check-circle" style="color: #4CAF50;"></i>
                <h4>Image Dropped Successfully</h4>
                <p>${file.name}</p>
                <p class="upload-note">Ready for analysis</p>
            `;
        }
    }
}

// Handle image analysis
async function handleImageAnalysis() {
    if (!selectedImage) return;

    const formData = new FormData();
    formData.append('image', selectedImage);

    // Close modal and show loading
    closeImageModal();
    showTypingIndicator();

    // Hide welcome elements
    if (welcomeCard) welcomeCard.style.display = 'none';
    if (quickReplies) quickReplies.style.display = 'none';

    try {
        // Choose the appropriate endpoint based on analysis type
        let endpoint = '/analyze-image';
        if (analysisType === 'tree_identification') {
            endpoint = '/identify-tree-plant';
        } else if (analysisType === 'medicinal') {
            endpoint = '/analyze-medicinal-plant';
        } else if (analysisType === 'forest') {
            endpoint = '/forest-analysis';
            formData.append('focus', 'ecological');
        }

        // Add analysis type for regular analyze-image endpoint
        if (endpoint === '/analyze-image') {
            formData.append('analysis_type', analysisType);
        }

        const response = await fetch(endpoint, {
            method: 'POST',
            body: formData
        });

        const data = await response.json();

        if (data.status === 'success') {
            // Add image message first
            addImageMessage(selectedImage, 'user');

            // Add analysis result
            const result = data.analysis || data.identification || data.response;
            addMessage(result, 'bot');
            messageHistory.push({role: 'assistant', content: result});
        } else {
            throw new Error(data.error || 'Failed to analyze image');
        }

    } catch (error) {
        console.error('Error:', error);
        addMessage('Sorry, I encountered an error while analyzing the image. Please try again.', 'bot', true);
    } finally {
        hideTypingIndicator();
        selectedImage = null;
    }
}

// Handle sending messages (updated to support images)
async function handleSendMessage() {
    const message = messageInput.value.trim();

    if ((!message && !selectedImage) || isTyping) return;

    // Hide welcome card and quick replies after first message
    if (messageHistory.length === 0) {
        if (welcomeCard) welcomeCard.style.display = 'none';
        if (quickReplies) quickReplies.style.display = 'none';
    }

    // Show typing indicator
    showTypingIndicator();

    try {
        if (selectedImage) {
            // Send message with image
            const formData = new FormData();
            formData.append('image', selectedImage);
            formData.append('message', message || 'Please analyze this agricultural image');

            // Add user message with image
            addImageMessage(selectedImage, 'user', message);

            const response = await fetch('/chat-with-image', {
                method: 'POST',
                body: formData
            });

            const data = await response.json();

            if (data.status === 'success') {
                addMessage(data.response, 'bot');
                messageHistory.push({role: 'assistant', content: data.response});
            } else {
                throw new Error(data.error || 'Failed to process image');
            }

            // Clear image
            removeSelectedImage();

        } else {
            // Regular text message
            addMessage(message, 'user');
            messageHistory.push({role: 'user', content: message});

            const response = await fetch('/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message: message
                })
            });

            const data = await response.json();

            if (data.status === 'success') {
                addMessage(data.response, 'bot');
                messageHistory.push({role: 'assistant', content: data.response});
            } else {
                throw new Error(data.error || 'Unknown error occurred');
            }
        }

    } catch (error) {
        console.error('Error:', error);
        const errorMessage = error.message.includes('API key')
            ? 'Please configure your OpenAI API key in the .env file to use the chatbot.'
            : 'Sorry, I encountered an error. Please check your internet connection and try again.';
        addMessage(errorMessage, 'bot', true);
    } finally {
        // Clear input and reset
        messageInput.value = '';
        updateCharCount();
        autoResizeTextarea();
        updateSendButton();
        hideTypingIndicator();
        messageInput.focus();
    }
}

// Add message to chat
function addMessage(content, sender, isError = false) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}`;

    const avatar = document.createElement('div');
    avatar.className = 'message-avatar';
    avatar.innerHTML = sender === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';

    const messageContent = document.createElement('div');
    messageContent.className = 'message-content';

    if (isError) {
        messageContent.style.background = '#ffebee';
        messageContent.style.borderColor = '#f44336';
        messageContent.style.color = '#c62828';
    }

    // Format message content
    messageContent.innerHTML = formatMessage(content);

    const messageTime = document.createElement('div');
    messageTime.className = 'message-time';
    messageTime.textContent = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});

    messageDiv.appendChild(avatar);
    const contentWrapper = document.createElement('div');
    contentWrapper.appendChild(messageContent);
    contentWrapper.appendChild(messageTime);
    messageDiv.appendChild(contentWrapper);

    chatMessages.appendChild(messageDiv);
    scrollToBottom();
}

// Add image message to chat
function addImageMessage(imageFile, sender, text = '') {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}`;

    const avatar = document.createElement('div');
    avatar.className = 'message-avatar';
    avatar.innerHTML = sender === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';

    const messageContent = document.createElement('div');
    messageContent.className = 'message-content image-message';

    // Create image element
    const img = document.createElement('img');
    img.className = 'message-image';
    img.style.maxWidth = '200px';
    img.style.maxHeight = '200px';
    img.style.borderRadius = '8px';
    img.style.marginBottom = text ? '10px' : '0';

    // Create image URL
    const reader = new FileReader();
    reader.onload = function(e) {
        img.src = e.target.result;
    };
    reader.readAsDataURL(imageFile);

    messageContent.appendChild(img);

    // Add text if provided
    if (text) {
        const textDiv = document.createElement('div');
        textDiv.innerHTML = formatMessage(text);
        messageContent.appendChild(textDiv);
    }

    const messageTime = document.createElement('div');
    messageTime.className = 'message-time';
    messageTime.textContent = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});

    messageDiv.appendChild(avatar);
    const contentWrapper = document.createElement('div');
    contentWrapper.appendChild(messageContent);
    contentWrapper.appendChild(messageTime);
    messageDiv.appendChild(contentWrapper);

    chatMessages.appendChild(messageDiv);
    scrollToBottom();
}

// Format message content
function formatMessage(content) {
    // Convert line breaks to <br>
    content = content.replace(/\n/g, '<br>');
    
    // Make text in **bold** actually bold
    content = content.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    
    // Make text in *italic* actually italic
    content = content.replace(/\*(.*?)\*/g, '<em>$1</em>');
    
    // Convert URLs to clickable links
    const urlRegex = /(https?:\/\/[^\s]+)/g;
    content = content.replace(urlRegex, '<a href="$1" target="_blank" rel="noopener noreferrer">$1</a>');
    
    return content;
}

// Show typing indicator
function showTypingIndicator() {
    isTyping = true;
    typingIndicator.style.display = 'flex';
    sendButton.disabled = true;
    scrollToBottom();
}

// Hide typing indicator
function hideTypingIndicator() {
    isTyping = false;
    typingIndicator.style.display = 'none';
    sendButton.disabled = false;
}

// Update character count
function updateCharCount() {
    const count = messageInput.value.length;
    if (charCount) {
        charCount.textContent = `${count}/500`;

        if (count > 450) {
            charCount.style.color = '#f44336';
        } else if (count > 400) {
            charCount.style.color = '#ff9800';
        } else {
            charCount.style.color = '#666';
        }
    }
}

// Auto-resize textarea
function autoResizeTextarea() {
    messageInput.style.height = 'auto';
    messageInput.style.height = Math.min(messageInput.scrollHeight, 120) + 'px';
}

// Update send button state
function updateSendButton() {
    const hasText = messageInput.value.trim().length > 0;
    sendButton.disabled = !hasText || isTyping;
}

// Scroll to bottom of chat
function scrollToBottom() {
    setTimeout(() => {
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }, 100);
}

// Show loading overlay
function showLoading() {
    loadingOverlay.style.display = 'flex';
}

// Hide loading overlay
function hideLoading() {
    loadingOverlay.style.display = 'none';
}

// Error handling for network issues
window.addEventListener('online', function() {
    console.log('Connection restored');
});

window.addEventListener('offline', function() {
    addMessage('You appear to be offline. Please check your internet connection.', 'bot', true);
});

// Prevent form submission on enter in input
messageInput.addEventListener('keydown', function(e) {
    if (e.key === 'Enter' && e.shiftKey) {
        // Allow Shift+Enter for new lines
        return;
    }
});

// Focus input when clicking anywhere in the chat area
chatMessages.addEventListener('click', function() {
    messageInput.focus();
});

// Add some helpful keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + / to focus input
    if ((e.ctrlKey || e.metaKey) && e.key === '/') {
        e.preventDefault();
        messageInput.focus();
    }

    // Escape to clear input or close menus
    if (e.key === 'Escape') {
        if (floatingMenu.classList.contains('active')) {
            closeFloatingMenu();
        } else if (sidebarOpen) {
            closeSidebar();
        } else {
            messageInput.value = '';
            updateCharCount();
            autoResizeTextarea();
            updateSendButton();
            messageInput.focus();
        }
    }

    // Ctrl/Cmd + K to clear chat
    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        clearChat();
    }
});

// Initialize mobile responsiveness
function initializeMobileFeatures() {
    // Create sidebar overlay if on mobile
    if (window.innerWidth <= 768) {
        createSidebarOverlay();
    }

    // Handle window resize
    window.addEventListener('resize', function() {
        if (window.innerWidth > 768) {
            closeSidebar();
            const overlay = document.querySelector('.sidebar-overlay');
            if (overlay) overlay.remove();
        } else if (!document.querySelector('.sidebar-overlay')) {
            createSidebarOverlay();
        }
    });
}

// Initialize mobile features
initializeMobileFeatures();

// Add smooth scrolling behavior
function smoothScrollToBottom() {
    const container = document.querySelector('.chat-messages-container');
    if (container) {
        container.scrollTo({
            top: container.scrollHeight,
            behavior: 'smooth'
        });
    }
}

// Enhanced scroll to bottom
function scrollToBottom() {
    setTimeout(smoothScrollToBottom, 100);
}

// Add loading states for better UX
function setLoadingState(loading) {
    if (loading) {
        sendButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        sendButton.disabled = true;
    } else {
        sendButton.innerHTML = '<i class="fas fa-paper-plane"></i>';
        updateSendButton();
    }
}

// Initialize new features
function initializeNewFeatures() {
    // Tree identification
    if (openTreeIdentificationBtn) {
        openTreeIdentificationBtn.addEventListener('click', function() {
            analysisType = 'tree_identification';
            openImageModal();
        });
    }

    // Tourist guide
    if (openTouristGuideBtn) {
        openTouristGuideBtn.addEventListener('click', openTouristGuideModal);
    }

    if (closeTouristGuideModalBtn) {
        closeTouristGuideModalBtn.addEventListener('click', closeTouristGuideModal);
    }

    // Itinerary planner
    if (openItineraryPlannerBtn) {
        openItineraryPlannerBtn.addEventListener('click', openItineraryModal);
    }

    if (closeItineraryModalBtn) {
        closeItineraryModalBtn.addEventListener('click', closeItineraryModal);
    }

    if (cancelItineraryBtn) {
        cancelItineraryBtn.addEventListener('click', closeItineraryModal);
    }

    // Add destination
    if (addDestinationBtn) {
        addDestinationBtn.addEventListener('click', addDestination);
    }

    if (destinationInput) {
        destinationInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                addDestination();
            }
        });
    }

    // Create itinerary
    if (createItineraryBtn) {
        createItineraryBtn.addEventListener('click', createItinerary);
    }
}

// Tourist Guide Modal Functions
function openTouristGuideModal() {
    if (touristGuideModal) {
        touristGuideModal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
        loadDistricts();
    }
}

function closeTouristGuideModal() {
    if (touristGuideModal) {
        touristGuideModal.style.display = 'none';
        document.body.style.overflow = '';
    }
}

function loadDistricts() {
    // Sample districts data - in a real app, this would come from the backend
    const districts = [
        { name: 'Chennai', region: 'Northern', attractions: 'Marina Beach, Kapaleeshwarar Temple, Fort St. George' },
        { name: 'Madurai', region: 'Central', attractions: 'Meenakshi Temple, Thirumalai Nayakkar Palace' },
        { name: 'Coimbatore', region: 'Western', attractions: 'Marudamalai Temple, Siruvani Waterfalls' },
        { name: 'Kanyakumari', region: 'Southern', attractions: 'Vivekananda Rock Memorial, Thiruvalluvar Statue' },
        { name: 'Thanjavur', region: 'Central', attractions: 'Brihadeeswarar Temple, Thanjavur Palace' }
    ];

    displayDistricts(districts);
}

function displayDistricts(districts) {
    if (!districtsGrid) return;

    districtsGrid.innerHTML = '';

    districts.forEach(district => {
        const districtCard = document.createElement('div');
        districtCard.className = 'district-card';
        districtCard.innerHTML = `
            <h4>${district.name}</h4>
            <div class="region">${district.region}</div>
            <div class="attractions">${district.attractions}</div>
        `;

        districtCard.addEventListener('click', () => {
            closeTouristGuideModal();
            const message = `Tell me everything about ${district.name} district in Tamil Nadu. Include attractions, culture, food, accommodation, and travel tips.`;
            messageInput.value = message;
            handleSendMessage();
        });

        districtsGrid.appendChild(districtCard);
    });
}

// Itinerary Modal Functions
function openItineraryModal() {
    if (itineraryModal) {
        itineraryModal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
        resetItineraryForm();
    }
}

function closeItineraryModal() {
    if (itineraryModal) {
        itineraryModal.style.display = 'none';
        document.body.style.overflow = '';
    }
}

function resetItineraryForm() {
    selectedDestinationsList = [];
    if (destinationInput) destinationInput.value = '';
    if (selectedDestinations) selectedDestinations.innerHTML = '';
}

function addDestination() {
    const destination = destinationInput.value.trim();
    if (destination && !selectedDestinationsList.includes(destination)) {
        selectedDestinationsList.push(destination);
        updateDestinationsDisplay();
        destinationInput.value = '';
    }
}

function updateDestinationsDisplay() {
    if (!selectedDestinations) return;

    selectedDestinations.innerHTML = '';
    selectedDestinationsList.forEach((destination, index) => {
        const tag = document.createElement('div');
        tag.className = 'destination-tag';
        tag.innerHTML = `
            ${destination}
            <button class="remove-destination" onclick="removeDestination(${index})">
                <i class="fas fa-times"></i>
            </button>
        `;
        selectedDestinations.appendChild(tag);
    });
}

function removeDestination(index) {
    selectedDestinationsList.splice(index, 1);
    updateDestinationsDisplay();
}

async function createItinerary() {
    if (selectedDestinationsList.length === 0) {
        alert('Please add at least one destination');
        return;
    }

    const duration = document.getElementById('tripDuration').value;
    const budget = document.getElementById('budgetLevel').value;
    const interests = Array.from(document.querySelectorAll('.interest-checkbox input:checked')).map(cb => cb.value);

    closeItineraryModal();
    showTypingIndicator();

    try {
        const response = await fetch('/travel-itinerary', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                destinations: selectedDestinationsList,
                duration: parseInt(duration),
                budget: budget,
                interests: interests
            })
        });

        const data = await response.json();

        if (data.status === 'success') {
            addMessage(`Here's your custom ${duration}-day Tamil Nadu itinerary:\n\n${data.itinerary}`, 'bot');
        } else {
            throw new Error(data.error || 'Failed to create itinerary');
        }

    } catch (error) {
        console.error('Error:', error);
        addMessage('Sorry, I encountered an error while creating your itinerary. Please try again.', 'bot', true);
    } finally {
        hideTypingIndicator();
    }
}
