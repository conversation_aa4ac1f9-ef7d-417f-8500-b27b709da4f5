// DOM Elements
const messageInput = document.getElementById('messageInput');
const sendButton = document.getElementById('sendButton');
const chatMessages = document.getElementById('chatMessages');
const typingIndicator = document.getElementById('typingIndicator');
const charCount = document.getElementById('charCount');
const quickSuggestions = document.getElementById('quickSuggestions');
const welcomeSection = document.getElementById('welcomeSection');
const loadingOverlay = document.getElementById('loadingOverlay');

// State
let isTyping = false;
let messageHistory = [];

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeEventListeners();
    addWelcomeMessage();
    messageInput.focus();
});

// Event Listeners
function initializeEventListeners() {
    // Send button click
    sendButton.addEventListener('click', handleSendMessage);
    
    // Enter key press
    messageInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            handleSendMessage();
        }
    });
    
    // Character count
    messageInput.addEventListener('input', updateCharCount);
    
    // Quick suggestions
    quickSuggestions.addEventListener('click', function(e) {
        if (e.target.classList.contains('suggestion-item')) {
            const message = e.target.getAttribute('data-message');
            messageInput.value = message;
            handleSendMessage();
        }
    });
    
    // Auto-resize input
    messageInput.addEventListener('input', autoResizeInput);
}

// Add welcome message
function addWelcomeMessage() {
    const welcomeText = "வணக்கம்! (Vanakkam!) Welcome to Tamil Nadu! I'm your travel guide ready to help you explore this beautiful state. What would you like to know about Tamil Nadu?";
    addMessage(welcomeText, 'bot');
}

// Handle sending messages
async function handleSendMessage() {
    const message = messageInput.value.trim();
    
    if (!message || isTyping) return;
    
    // Hide welcome section and quick suggestions after first message
    if (messageHistory.length === 0) {
        welcomeSection.style.display = 'none';
        quickSuggestions.style.display = 'none';
    }
    
    // Add user message
    addMessage(message, 'user');
    messageHistory.push({role: 'user', content: message});
    
    // Clear input
    messageInput.value = '';
    updateCharCount();
    
    // Show typing indicator
    showTypingIndicator();
    
    try {
        // Send to backend
        const response = await fetch('/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                message: message
            })
        });
        
        const data = await response.json();
        
        if (data.status === 'success') {
            // Add bot response
            addMessage(data.response, 'bot');
            messageHistory.push({role: 'assistant', content: data.response});
        } else {
            throw new Error(data.error || 'Unknown error occurred');
        }
        
    } catch (error) {
        console.error('Error:', error);
        addMessage('Sorry, I encountered an error. Please check your internet connection and try again. If the problem persists, make sure your OpenAI API key is properly configured.', 'bot', true);
    } finally {
        hideTypingIndicator();
        messageInput.focus();
    }
}

// Add message to chat
function addMessage(content, sender, isError = false) {
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}`;
    
    const avatar = document.createElement('div');
    avatar.className = 'message-avatar';
    avatar.innerHTML = sender === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';
    
    const messageContent = document.createElement('div');
    messageContent.className = 'message-content';
    
    if (isError) {
        messageContent.style.background = '#ffebee';
        messageContent.style.borderColor = '#f44336';
        messageContent.style.color = '#c62828';
    }
    
    // Format message content
    messageContent.innerHTML = formatMessage(content);
    
    const messageTime = document.createElement('div');
    messageTime.className = 'message-time';
    messageTime.textContent = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    
    messageDiv.appendChild(avatar);
    const contentWrapper = document.createElement('div');
    contentWrapper.appendChild(messageContent);
    contentWrapper.appendChild(messageTime);
    messageDiv.appendChild(contentWrapper);
    
    chatMessages.appendChild(messageDiv);
    scrollToBottom();
}

// Format message content
function formatMessage(content) {
    // Convert line breaks to <br>
    content = content.replace(/\n/g, '<br>');
    
    // Make text in **bold** actually bold
    content = content.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    
    // Make text in *italic* actually italic
    content = content.replace(/\*(.*?)\*/g, '<em>$1</em>');
    
    // Convert URLs to clickable links
    const urlRegex = /(https?:\/\/[^\s]+)/g;
    content = content.replace(urlRegex, '<a href="$1" target="_blank" rel="noopener noreferrer">$1</a>');
    
    return content;
}

// Show typing indicator
function showTypingIndicator() {
    isTyping = true;
    typingIndicator.style.display = 'flex';
    sendButton.disabled = true;
    scrollToBottom();
}

// Hide typing indicator
function hideTypingIndicator() {
    isTyping = false;
    typingIndicator.style.display = 'none';
    sendButton.disabled = false;
}

// Update character count
function updateCharCount() {
    const count = messageInput.value.length;
    charCount.textContent = `${count}/500`;
    
    if (count > 450) {
        charCount.style.color = '#f44336';
    } else if (count > 400) {
        charCount.style.color = '#ff9800';
    } else {
        charCount.style.color = '#666';
    }
}

// Auto-resize input
function autoResizeInput() {
    messageInput.style.height = 'auto';
    messageInput.style.height = Math.min(messageInput.scrollHeight, 120) + 'px';
}

// Scroll to bottom of chat
function scrollToBottom() {
    setTimeout(() => {
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }, 100);
}

// Show loading overlay
function showLoading() {
    loadingOverlay.style.display = 'flex';
}

// Hide loading overlay
function hideLoading() {
    loadingOverlay.style.display = 'none';
}

// Error handling for network issues
window.addEventListener('online', function() {
    console.log('Connection restored');
});

window.addEventListener('offline', function() {
    addMessage('You appear to be offline. Please check your internet connection.', 'bot', true);
});

// Prevent form submission on enter in input
messageInput.addEventListener('keydown', function(e) {
    if (e.key === 'Enter' && e.shiftKey) {
        // Allow Shift+Enter for new lines
        return;
    }
});

// Focus input when clicking anywhere in the chat area
chatMessages.addEventListener('click', function() {
    messageInput.focus();
});

// Add some helpful keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + / to focus input
    if ((e.ctrlKey || e.metaKey) && e.key === '/') {
        e.preventDefault();
        messageInput.focus();
    }
    
    // Escape to clear input
    if (e.key === 'Escape') {
        messageInput.value = '';
        updateCharCount();
        messageInput.focus();
    }
});
