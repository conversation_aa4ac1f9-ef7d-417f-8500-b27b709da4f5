#!/usr/bin/env python3
"""
Test script for local model integration in Vaanga Suthalam
This script tests the local model loading and basic functionality
"""

import os
import sys

def test_local_model():
    """Test local model loading and basic functionality"""
    
    print("🚀 Testing Vaanga Suthalam Local Model Integration")
    print("=" * 60)
    
    # Test 1: Check if llama-cpp-python is available
    print("\n1. Testing llama-cpp-python availability...")
    try:
        from llama_cpp import Llama
        print("✅ llama-cpp-python is available")
    except ImportError as e:
        print(f"❌ llama-cpp-python not available: {e}")
        print("💡 Install with: pip install llama-cpp-python")
        return False
    
    # Test 2: Check if model file exists
    print("\n2. Checking model file...")
    model_path = os.path.join(os.path.dirname(__file__), 
                             "models", "bartowski", "Llama-3.2-3B-Instruct-GGUF", 
                             "Llama-3.2-3B-Instruct-Q4_K_S.gguf")
    
    if os.path.exists(model_path):
        print(f"✅ Model file found: {model_path}")
        file_size = os.path.getsize(model_path) / (1024 * 1024)  # MB
        print(f"📊 Model size: {file_size:.1f} MB")
    else:
        print(f"❌ Model file not found: {model_path}")
        print("💡 Please ensure the model file is in the correct location")
        return False
    
    # Test 3: Try loading the model
    print("\n3. Testing model loading...")
    try:
        print("🔄 Loading model (this may take a moment)...")
        model = Llama(
            model_path=model_path,
            n_ctx=2048,  # Smaller context for testing
            n_threads=2,  # Fewer threads for testing
            verbose=False
        )
        print("✅ Model loaded successfully!")
        
        # Test 4: Generate a simple response
        print("\n4. Testing text generation...")
        test_prompt = """<|begin_of_text|><|start_header_id|>system<|end_header_id|>

You are a helpful assistant specializing in Tamil Nadu tourism and agriculture.<|eot_id|><|start_header_id|>user<|end_header_id|>

Tell me about Tamil Nadu in one sentence.<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""
        
        response = model(
            test_prompt,
            max_tokens=100,
            temperature=0.7,
            stop=["</s>", "Human:", "Assistant:"],
            echo=False
        )
        
        generated_text = response['choices'][0]['text'].strip()
        print(f"✅ Generated response: {generated_text}")
        
        print("\n🎉 All tests passed! Local model integration is working.")
        return True
        
    except Exception as e:
        print(f"❌ Error loading or testing model: {e}")
        print("💡 Check if you have enough RAM and the model file is not corrupted")
        return False

def test_flask_app():
    """Test if the Flask app can start with local model"""
    print("\n5. Testing Flask app integration...")
    try:
        # Import the app
        from app import app, local_model, model_loaded
        
        if model_loaded and local_model:
            print("✅ Flask app loaded with local model successfully")
            print("🌐 You can now run: python app.py")
            return True
        else:
            print("❌ Flask app loaded but local model not available")
            return False
            
    except Exception as e:
        print(f"❌ Error testing Flask app: {e}")
        return False

if __name__ == "__main__":
    print("Vaanga Suthalam - Local Model Test")
    print("This script tests the local AI model integration")
    print()
    
    # Run tests
    model_test = test_local_model()
    
    if model_test:
        flask_test = test_flask_app()
        
        if flask_test:
            print("\n🎊 SUCCESS! Your Vaanga Suthalam app is ready with local AI!")
            print("\n📋 Next steps:")
            print("1. Run: python app.py")
            print("2. Open: http://localhost:5000")
            print("3. Upload plant images for AI analysis")
            print("4. Chat about Tamil Nadu tourism and agriculture")
        else:
            print("\n⚠️  Model works but Flask integration needs attention")
    else:
        print("\n❌ Local model setup needs to be fixed first")
        print("\n🔧 Troubleshooting:")
        print("1. Install: pip install llama-cpp-python")
        print("2. Ensure model file is in: models/bartowski/Llama-3.2-3B-Instruct-GGUF/")
        print("3. Check available RAM (model needs ~2-4GB)")
