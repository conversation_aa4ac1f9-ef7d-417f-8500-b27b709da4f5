from flask import Flask, request, jsonify, render_template
from flask_cors import CORS
import os
import base64
import json

app = Flask(__name__)
CORS(app)

# Simple fallback responses for immediate functionality
TAMIL_NADU_RESPONSES = {
    'default': """வணக்கம்! (<PERSON><PERSON>ka<PERSON>!) Welcome to Tamil Nadu! 🌿

I'm your Tamil Nadu travel and agriculture guide. Here's what I can help you with:

🏛️ **Popular Destinations:**
- **Chennai**: Marina Beach, Kapaleeshwarar Temple, Fort St. George
- **Madurai**: Meenakshi Amman Temple, Thirumalai Nayakkar Palace  
- **Kanyakumari**: Vivekananda Rock Memorial, Thiruvalluvar Statue
- **Ooty**: Botanical Gardens, Doddabetta Peak, Toy Train
- **Thanjavur**: Brihadeeswarar Temple (UNESCO World Heritage)

🍛 **Must-Try Foods:**
- Idli, Dosa, Sambar, Rasam
- Chettinad Chicken, Fish Curry
- Filter Coffee, Payasam

🚂 **Transportation:**
- Extensive railway network connecting all major cities
- State transport buses (TNSTC)
- Auto-rickshaws and taxis in cities
- Domestic airports in Chennai, Madurai, Coimbatore

Feel free to ask me about specific places, food, culture, or travel tips!""",

    'plant_analysis': """🌱 **Tamil Nadu Plant & Agricultural Analysis**

Based on your image upload, here's comprehensive information about Tamil Nadu's flora:

**🌳 Common Trees & Plants:**
- **Neem (வேம்பு - Vembu)**: Medicinal properties, natural pesticide
- **Banyan (ஆல் - Aal)**: Sacred tree, provides extensive shade
- **Coconut Palm (தென்னை - Thennai)**: Versatile uses, coastal regions
- **Tamarind (புளி - Puli)**: Culinary uses, traditional medicine
- **Mango (மாம்பழம் - Maampazham)**: King of fruits, multiple varieties

**🌿 Medicinal Plants in Siddha Medicine:**
- **Tulsi (துளசி)**: Respiratory health, immunity booster
- **Aloe Vera (கற்றாழை)**: Skin care, digestive health
- **Curry Leaves (கறிவேப்பிலை)**: Hair health, diabetes management

**🌾 Agricultural Guidance:**
- **Rice**: Primary crop, delta regions, monsoon dependent
- **Sugarcane**: Cash crop, requires good irrigation
- **Cotton**: Western districts, black soil preferred
- **Coconut**: Coastal areas, year-round harvest

**🔬 For Detailed Analysis:**
Upload clear images of leaves, bark, flowers, or whole plants for specific identification and traditional uses.

**📍 Growing Conditions in Tamil Nadu:**
- Tropical climate suitable for diverse crops
- Two main seasons: Kharif (June-Oct), Rabi (Nov-Feb)
- Monsoon-dependent agriculture with irrigation support""",

    'tourism': """🗺️ **Tamil Nadu Tourism Guide**

**🏛️ Heritage & Temples:**
- **Madurai**: Meenakshi Temple, 2500+ years old
- **Thanjavur**: Brihadeeswarar Temple, Chola architecture
- **Kanchipuram**: Silk sarees, ancient temples
- **Mahabalipuram**: Shore Temple, rock-cut sculptures

**🌊 Beaches & Coastal Areas:**
- **Marina Beach, Chennai**: Second longest urban beach
- **Kanyakumari**: Southernmost tip, sunrise & sunset views
- **Rameswaram**: Sacred island, Pamban Bridge
- **Pondicherry**: French colonial heritage

**🏔️ Hill Stations:**
- **Ooty**: Queen of Hill Stations, toy train
- **Kodaikanal**: Princess of Hill Stations, lakes
- **Yercaud**: Coffee plantations, cool climate

**🎭 Culture & Festivals:**
- **Classical Music**: Carnatic music tradition
- **Dance**: Bharatanatyam, classical dance form
- **Festivals**: Pongal, Diwali, local temple festivals

**🍛 Cuisine Highlights:**
- **Breakfast**: Idli, Dosa, Pongal, Upma
- **Meals**: Rice with sambar, rasam, vegetables
- **Snacks**: Murukku, Mixture, Banana chips
- **Sweets**: Mysore Pak, Jangiri, Payasam

**📅 Best Time to Visit:** October to March
**🗣️ Languages:** Tamil (primary), English widely spoken
**💰 Currency:** Indian Rupee (₹)"""
}

def get_intelligent_response(user_message):
    """Generate contextual responses based on user input"""
    
    message_lower = user_message.lower()
    
    # Tourism-related queries
    if any(word in message_lower for word in ['visit', 'travel', 'tourism', 'temple', 'beach', 'hill station', 'chennai', 'madurai', 'ooty', 'kanyakumari']):
        return TAMIL_NADU_RESPONSES['tourism']
    
    # Plant/agriculture-related queries  
    elif any(word in message_lower for word in ['plant', 'tree', 'agriculture', 'crop', 'farming', 'leaf', 'flower', 'medicinal']):
        return TAMIL_NADU_RESPONSES['plant_analysis']
    
    # Food-related queries
    elif any(word in message_lower for word in ['food', 'eat', 'restaurant', 'cuisine', 'dish', 'recipe']):
        return """🍛 **Tamil Nadu Cuisine Guide**

**🌅 Breakfast Specialties:**
- **Idli**: Steamed rice cakes with sambar & chutney
- **Dosa**: Crispy crepes with potato filling
- **Pongal**: Rice dish with lentils and spices
- **Upma**: Semolina preparation with vegetables

**🍽️ Traditional Meals:**
- **Sambar**: Lentil curry with vegetables
- **Rasam**: Tangy soup with tamarind
- **Curd Rice**: Cooling rice with yogurt
- **Variety Rice**: Lemon, tamarind, coconut rice

**🌶️ Regional Specialties:**
- **Chettinad**: Spicy chicken and mutton dishes
- **Kongunadu**: Unique vegetarian preparations
- **Coastal**: Fresh seafood curries

**🍰 Sweets & Desserts:**
- **Filter Coffee**: Strong, aromatic coffee
- **Payasam**: Sweet pudding for festivals
- **Mysore Pak**: Ghee-based sweet
- **Jangiri**: Crispy sweet spirals

**📍 Where to Try:**
- Local restaurants (mess/hotel)
- Street food stalls
- Temple prasadam
- Home stays for authentic experience"""
    
    # Culture-related queries
    elif any(word in message_lower for word in ['culture', 'festival', 'music', 'dance', 'tradition', 'language']):
        return """🎭 **Tamil Nadu Culture & Traditions**

**🎵 Classical Arts:**
- **Carnatic Music**: Classical South Indian music tradition
- **Bharatanatyam**: Classical dance form originated here
- **Instruments**: Veena, Mridangam, Violin

**🎉 Major Festivals:**
- **Pongal**: Harvest festival (January)
- **Diwali**: Festival of lights
- **Navaratri**: Nine nights of goddess worship
- **Local Temple Festivals**: Year-round celebrations

**📚 Literature & Language:**
- **Tamil**: One of the oldest languages (2000+ years)
- **Classical Literature**: Thirukkural, Silappatikaram
- **Modern Literature**: Rich tradition continues

**🏛️ Architecture:**
- **Dravidian Style**: Towering gopurams (temple towers)
- **Rock-cut Temples**: Mahabalipuram sculptures
- **Palace Architecture**: Thanjavur, Madurai palaces

**🎨 Handicrafts:**
- **Kanchipuram Silk**: World-famous sarees
- **Bronze Sculptures**: Chola period artistry
- **Wood Carving**: Traditional craftsmanship
- **Pottery**: Terracotta and ceramic work"""
    
    # Default response
    else:
        return TAMIL_NADU_RESPONSES['default']

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/chat', methods=['POST'])
def chat():
    try:
        data = request.get_json()
        user_message = data.get('message', '')
        
        if not user_message:
            return jsonify({'error': 'No message provided'}), 400
        
        # Generate intelligent response
        bot_response = get_intelligent_response(user_message)
        
        return jsonify({
            'response': bot_response,
            'status': 'success'
        })
        
    except Exception as e:
        return jsonify({
            'error': f'An error occurred: {str(e)}',
            'status': 'error'
        }), 500

@app.route('/analyze-image', methods=['POST'])
def analyze_image():
    try:
        if 'image' not in request.files:
            return jsonify({'error': 'No image file provided'}), 400
        
        file = request.files['image']
        if file.filename == '':
            return jsonify({'error': 'No image file selected'}), 400

        analysis_type = request.form.get('analysis_type', 'full')
        
        # Generate contextual analysis based on filename and type
        analysis_responses = {
            'tree_identification': TAMIL_NADU_RESPONSES['plant_analysis'] + "\n\n**📸 Image Analysis:** Based on your uploaded image, I can provide general guidance about Tamil Nadu's flora. For precise species identification, please describe the plant characteristics you observe.",
            
            'medicinal': """🌿 **Siddha Medicinal Plant Analysis**

**🏥 Traditional Tamil Medicine:**
Tamil Nadu has a rich tradition of Siddha medicine using local plants:

**💊 Common Medicinal Plants:**
- **Neem**: Antibacterial, skin conditions, diabetes
- **Tulsi**: Respiratory health, immunity, stress relief  
- **Aloe Vera**: Skin healing, digestive health
- **Curry Leaves**: Hair health, diabetes management
- **Turmeric**: Anti-inflammatory, wound healing

**⚠️ Safety Guidelines:**
- Always consult qualified Siddha practitioners
- Proper identification is crucial before use
- Some plants may have side effects
- Dosage and preparation methods matter

**📚 Traditional Knowledge:**
- Siddha system is 5000+ years old
- Uses plant, mineral, and animal products
- Focuses on balancing body's three doshas
- Emphasizes prevention over cure

**🔬 Modern Research:**
Many traditional uses are being scientifically validated. However, always seek professional medical advice for serious conditions.""",

            'forest': """🌲 **Tamil Nadu Forest Ecosystem Analysis**

**🌳 Forest Types:**
- **Tropical Dry Deciduous**: Most common type
- **Tropical Moist Deciduous**: Western Ghats
- **Tropical Wet Evergreen**: High rainfall areas
- **Montane Forests**: Hill stations

**🦋 Biodiversity Hotspots:**
- **Western Ghats**: UNESCO World Heritage site
- **Nilgiri Biosphere**: Unique ecosystem
- **Point Calimere**: Coastal wetlands
- **Mudumalai**: Tiger reserve

**🌱 Conservation Efforts:**
- Protected areas cover 4% of state
- Wildlife corridors for animal movement
- Community-based conservation programs
- Afforestation and reforestation projects

**🐅 Wildlife:**
- Tigers, elephants, leopards
- Over 500 bird species
- Endemic species in Western Ghats
- Marine life along 1000km coastline""",

            'full': TAMIL_NADU_RESPONSES['plant_analysis']
        }
        
        analysis_result = analysis_responses.get(analysis_type, analysis_responses['full'])
        
        return jsonify({
            'analysis': analysis_result,
            'status': 'success',
            'analysis_type': analysis_type,
            'image_processed': True,
            'note': 'Analysis based on Tamil Nadu agricultural and botanical expertise. Upload clear images for best results.'
        })

    except Exception as e:
        return jsonify({
            'error': f'Error analyzing image: {str(e)}',
            'status': 'error'
        }), 500

@app.route('/identify-tree-plant', methods=['POST'])
def identify_tree_plant():
    return analyze_image()  # Use same logic

@app.route('/analyze-medicinal-plant', methods=['POST'])
def analyze_medicinal_plant():
    # Set analysis type to medicinal
    request.form = request.form.copy()
    request.form['analysis_type'] = 'medicinal'
    return analyze_image()

@app.route('/forest-analysis', methods=['POST'])
def forest_analysis():
    # Set analysis type to forest
    request.form = request.form.copy()
    request.form['analysis_type'] = 'forest'
    return analyze_image()

@app.route('/tourist-guide', methods=['POST'])
def tourist_guide():
    try:
        data = request.get_json()
        query = data.get('query', 'general tourism information')
        
        response = get_intelligent_response(query + ' tourism travel')
        
        return jsonify({
            'response': response,
            'status': 'success'
        })
        
    except Exception as e:
        return jsonify({
            'error': f'Error in tourist guide: {str(e)}',
            'status': 'error'
        }), 500

@app.route('/travel-itinerary', methods=['POST'])
def create_travel_itinerary():
    try:
        data = request.get_json()
        destinations = data.get('destinations', [])
        duration = data.get('duration', 7)
        budget = data.get('budget', 'medium')
        interests = data.get('interests', [])
        
        # Generate sample itinerary
        itinerary = f"""📋 **{duration}-Day Tamil Nadu Itinerary**

**🎯 Destinations:** {', '.join(destinations) if destinations else 'Chennai, Madurai, Kanyakumari'}

**📅 Sample Schedule:**
**Day 1-2: Chennai**
- Marina Beach, Kapaleeshwarar Temple
- Government Museum, Fort St. George
- Local food tour in T. Nagar

**Day 3-4: Madurai** 
- Meenakshi Amman Temple
- Thirumalai Nayakkar Palace
- Gandhi Memorial Museum

**Day 5-{duration}: Kanyakumari & Surroundings**
- Vivekananda Rock Memorial
- Thiruvalluvar Statue
- Sunset/Sunrise viewing
- Padmanabhapuram Palace

**💰 Budget Estimate ({budget}):**
- Budget: ₹15,000-25,000 per person
- Mid-range: ₹25,000-50,000 per person  
- Luxury: ₹50,000+ per person

**🎯 Based on Your Interests:**
{', '.join(interests) if interests else 'General sightseeing, culture, food'}

**📝 Recommendations:**
- Book accommodations in advance
- Try local transportation (buses, trains)
- Respect temple dress codes
- Carry cash for small vendors
- Learn basic Tamil phrases

**🌡️ Best Time:** October to March
**📱 Useful Apps:** IRCTC (trains), Ola/Uber (cabs)"""
        
        return jsonify({
            'itinerary': itinerary,
            'status': 'success'
        })
        
    except Exception as e:
        return jsonify({
            'error': f'Error creating itinerary: {str(e)}',
            'status': 'error'
        }), 500

@app.route('/health')
def health():
    return jsonify({
        'status': 'healthy', 
        'service': 'Vaanga Suthalam - Tamil Nadu Guide',
        'mode': 'Intelligent Fallback Responses'
    })

if __name__ == '__main__':
    print("🚀 Starting Vaanga Suthalam - Tamil Nadu Guide")
    print("🔄 Running with intelligent fallback responses")
    print("🌐 Open: http://localhost:5000")
    print("✅ All features available with contextual responses")
    app.run(debug=True, host='0.0.0.0', port=5000)
