from flask import Flask, render_template, jsonify, request
from flask_cors import CORS
import os

app = Flask(__name__)
CORS(app)

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/chat', methods=['POST'])
def chat():
    try:
        user_message = request.json.get('message', '')
        
        # Mock response for testing
        mock_response = f"""வணக்கம்! Thanks for your message: "{user_message}"

🏛️ **Tamil Nadu Highlights:**
- Chennai: Marina Beach, Kapaleeshwarar Temple
- Madurai: Meenakshi Temple, cultural capital
- Kanyakumari: Southernmost tip of India
- Ooty: Hill station, tea gardens

🍛 **Must-try Foods:**
- Idli, Dosa, Sambar
- Chettinad Chicken
- Filter Coffee

Feel free to ask about specific places, food, or travel tips!"""
        
        return jsonify({
            'response': mock_response,
            'status': 'success'
        })
        
    except Exception as e:
        return jsonify({
            'error': f'An error occurred: {str(e)}',
            'status': 'error'
        }), 500

@app.route('/analyze-image', methods=['POST'])
def analyze_image():
    try:
        mock_response = """🌳 **Tree/Plant Analysis**

Based on your image, this appears to be a common Tamil Nadu plant!

**Identification:**
- Scientific Name: [Analysis would appear here]
- Tamil Name: [Tamil name would appear here]
- Common Uses: Traditional medicine, cooking, etc.

**Cultural Significance:**
Many plants in Tamil Nadu have deep cultural and religious significance.

Upload more images for detailed identification!"""
        
        return jsonify({
            'analysis': mock_response,
            'status': 'success'
        })
        
    except Exception as e:
        return jsonify({
            'error': f'Error analyzing image: {str(e)}',
            'status': 'error'
        }), 500

@app.route('/identify-tree-plant', methods=['POST'])
def identify_tree_plant():
    return analyze_image()  # Use same mock for now

@app.route('/tourist-guide', methods=['POST'])
def tourist_guide():
    try:
        mock_response = """🗺️ **Tamil Nadu District Guide**

**Popular Districts:**
- Chennai: Capital, Marina Beach, IT hub
- Madurai: Temple city, cultural heritage
- Coimbatore: Industrial city, Western Ghats
- Kanyakumari: Southernmost point, beaches

**Travel Tips:**
- Best time: October to March
- Transportation: Extensive rail network
- Language: Tamil, English widely spoken
- Currency: Indian Rupee (₹)

Click on any district for detailed information!"""
        
        return jsonify({
            'response': mock_response,
            'status': 'success'
        })
        
    except Exception as e:
        return jsonify({
            'error': f'Error in tourist guide: {str(e)}',
            'status': 'error'
        }), 500

@app.route('/travel-itinerary', methods=['POST'])
def create_travel_itinerary():
    try:
        data = request.get_json()
        destinations = data.get('destinations', [])
        duration = data.get('duration', 7)
        
        mock_itinerary = f"""📋 **{duration}-Day Tamil Nadu Itinerary**

**Destinations:** {', '.join(destinations)}

**Sample Schedule:**
Day 1-2: Arrival and Chennai exploration
Day 3-4: Cultural sites and temples
Day 5-6: Hill stations or coastal areas
Day 7: Shopping and departure

**Budget Estimate:** ₹15,000-25,000 per person
**Best Season:** October to March

**Included:**
- Accommodation suggestions
- Transportation options
- Must-visit attractions
- Local food recommendations

Customize based on your preferences!"""
        
        return jsonify({
            'itinerary': mock_itinerary,
            'status': 'success'
        })
        
    except Exception as e:
        return jsonify({
            'error': f'Error creating itinerary: {str(e)}',
            'status': 'error'
        }), 500

@app.route('/health')
def health():
    return jsonify({'status': 'healthy', 'service': 'Vaanga Suthalam - Tamil Nadu Guide'})

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
