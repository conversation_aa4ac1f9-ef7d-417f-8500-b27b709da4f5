#!/usr/bin/env python3
"""
Quick test to check if the app starts without errors
"""

import sys
import os

def test_app_startup():
    """Test if the Flask app can start without errors"""
    
    print("🧪 Testing Vaanga Suthalam App Startup")
    print("=" * 50)
    
    try:
        print("1. Testing imports...")
        
        # Test Flask imports
        from flask import Flask, request, jsonify, render_template
        from flask_cors import CORS
        print("✅ Flask imports successful")
        
        # Test if llama-cpp-python is available
        try:
            from llama_cpp import Llama
            print("✅ llama-cpp-python available")
            llama_available = True
        except ImportError:
            print("⚠️  llama-cpp-python not available")
            llama_available = False
        
        print("\n2. Testing app initialization...")
        
        # Import the app
        from app import app, local_model, model_loaded
        print("✅ App imported successfully")
        
        print(f"📊 Model loaded: {model_loaded}")
        print(f"🤖 Local model available: {local_model is not None}")
        
        print("\n3. Testing basic endpoints...")
        
        # Test if app can create test client
        with app.test_client() as client:
            print("✅ Test client created")
            
            # Test home page
            response = client.get('/')
            print(f"✅ Home page status: {response.status_code}")
            
            # Test health endpoint if it exists
            try:
                response = client.get('/health')
                print(f"✅ Health endpoint status: {response.status_code}")
            except:
                print("ℹ️  Health endpoint not available")
        
        print("\n🎉 App startup test completed successfully!")
        
        if not model_loaded:
            print("\n⚠️  WARNING: Local model not loaded")
            print("💡 The app will run with fallback responses")
            print("🔧 To fix: Ensure model file exists and llama-cpp-python is installed")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Install missing dependencies: pip install -r requirements.txt")
        return False
        
    except Exception as e:
        print(f"❌ Error during startup test: {e}")
        print("🔧 Check the error details above")
        return False

def test_chat_endpoint():
    """Test the chat endpoint with a simple message"""
    
    print("\n4. Testing chat functionality...")
    
    try:
        from app import app
        
        with app.test_client() as client:
            # Test chat endpoint
            response = client.post('/chat', 
                                 json={'message': 'Hello, tell me about Tamil Nadu'},
                                 content_type='application/json')
            
            print(f"✅ Chat endpoint status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.get_json()
                if data and 'response' in data:
                    print("✅ Chat response received")
                    print(f"📝 Response preview: {data['response'][:100]}...")
                else:
                    print("⚠️  Chat response format unexpected")
            else:
                print(f"⚠️  Chat endpoint returned error: {response.status_code}")
                
        return True
        
    except Exception as e:
        print(f"❌ Error testing chat: {e}")
        return False

if __name__ == "__main__":
    print("Vaanga Suthalam - App Startup Test")
    print("This script tests if the app can start without errors")
    print()
    
    # Test startup
    startup_ok = test_app_startup()
    
    if startup_ok:
        # Test chat functionality
        chat_ok = test_chat_endpoint()
        
        if chat_ok:
            print("\n🎊 SUCCESS! App is ready to run")
            print("\n📋 Next steps:")
            print("1. Run: python app.py")
            print("2. Open: http://localhost:5000")
            print("3. Test the chat and image upload features")
        else:
            print("\n⚠️  App starts but chat functionality has issues")
    else:
        print("\n❌ App startup failed - fix the errors above first")
        
    print("\n" + "="*50)
