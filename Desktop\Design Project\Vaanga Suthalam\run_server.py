#!/usr/bin/env python3
"""
Simple server runner that bypasses Flask console issues
"""

import sys
import os
import threading
import time

def run_server():
    """Run the server with proper error handling"""
    
    print("🚀 Vaanga Suthalam - Tamil Nadu AI Explorer")
    print("=" * 60)
    
    try:
        # Import the app
        from app import app, local_model, model_loaded
        
        print(f"🤖 Local Model: {'✅ Loaded Successfully' if model_loaded else '⚠️  Fallback Mode'}")
        print("🌐 Server URL: http://localhost:5000")
        print("📱 Open the URL in your browser")
        print("🛑 Press Ctrl+C to stop the server")
        print("-" * 60)
        
        # Method 1: Try Werkzeug directly
        try:
            from werkzeug.serving import run_simple
            print("✅ Starting with Werkzeug server...")
            
            run_simple(
                hostname='127.0.0.1',
                port=5000,
                application=app,
                use_reloader=False,
                use_debugger=False,
                threaded=True,
                use_evalex=False
            )
            
        except ImportError:
            print("⚠️  Werkzeug not available, trying alternative...")
            
            # Method 2: Use built-in WSGI server
            try:
                from wsgiref.simple_server import make_server
                print("✅ Starting with WSGI server...")
                
                server = make_server('127.0.0.1', 5000, app)
                print("✅ Server started successfully!")
                print("🌐 Open: http://localhost:5000")
                
                server.serve_forever()
                
            except Exception as e:
                print(f"⚠️  WSGI server error: {e}")
                
                # Method 3: Flask with output suppression
                print("🔄 Trying Flask with output suppression...")
                
                # Suppress Flask banner output
                import logging
                log = logging.getLogger('werkzeug')
                log.setLevel(logging.ERROR)
                
                # Redirect stdout/stderr temporarily
                original_stdout = sys.stdout
                original_stderr = sys.stderr
                
                try:
                    # Suppress output during startup
                    sys.stdout = open(os.devnull, 'w')
                    sys.stderr = open(os.devnull, 'w')
                    
                    # Start Flask in a thread
                    def flask_runner():
                        app.run(debug=False, host='127.0.0.1', port=5000, use_reloader=False)
                    
                    flask_thread = threading.Thread(target=flask_runner, daemon=True)
                    flask_thread.start()
                    
                    # Restore output
                    sys.stdout = original_stdout
                    sys.stderr = original_stderr
                    
                    print("✅ Server started in background!")
                    print("🌐 Open: http://localhost:5000")
                    
                    # Keep main thread alive
                    try:
                        while True:
                            time.sleep(1)
                    except KeyboardInterrupt:
                        print("\n🛑 Server stopped by user")
                        
                except Exception as flask_error:
                    # Restore output
                    sys.stdout = original_stdout
                    sys.stderr = original_stderr
                    print(f"❌ Flask startup error: {flask_error}")
                    
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure you're in the correct directory")
        print("📁 Current directory should contain app.py")
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        print("🔧 Try running the simple version: python app_simple.py")

def check_port():
    """Check if port 5000 is available"""
    
    import socket
    
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('127.0.0.1', 5000))
        sock.close()
        
        if result == 0:
            print("⚠️  Port 5000 is already in use")
            print("💡 Try closing other applications or use a different port")
            return False
        else:
            print("✅ Port 5000 is available")
            return True
            
    except Exception as e:
        print(f"⚠️  Could not check port: {e}")
        return True

def main():
    """Main function"""
    
    print("Vaanga Suthalam Server Launcher")
    print("Handles Windows console issues automatically")
    print()
    
    # Check port availability
    if not check_port():
        return
    
    # Run the server
    run_server()

if __name__ == "__main__":
    main()
