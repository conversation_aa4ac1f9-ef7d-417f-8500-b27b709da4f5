from flask import Flask, request, jsonify, render_template
from flask_cors import CORS
import os
import base64
import io
from PIL import Image
from dotenv import load_dotenv

# Try to import optional dependencies
try:
    from openai import OpenAI
    OPENAI_AVAILABLE = True
except ImportError:
    OPENAI_AVAILABLE = False
    print("OpenAI not available - using mock responses")

try:
    from flask_sqlalchemy import SQLAlchemy
    SQLALCHEMY_AVAILABLE = True
except ImportError:
    SQLALCHEMY_AVAILABLE = False
    print("SQLAlchemy not available - running without database")

try:
    from geopy.distance import geodesic
    from geopy.geocoders import Nominatim
    GEOPY_AVAILABLE = True
except ImportError:
    GEOPY_AVAILABLE = False
    print("Geopy not available - location features limited")

# Load environment variables
load_dotenv()

app = Flask(__name__)
CORS(app)

# Configure database if available
if SQLALCHEMY_AVAILABLE:
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///vaanga_suthalam.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    db = SQLAlchemy(app)

# Configure OpenAI if available
if OPENAI_AVAILABLE:
    client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))
else:
    client = None

# Initialize geocoder if available
if GEOPY_AVAILABLE:
    geolocator = Nominatim(user_agent="vaanga_suthalam")
else:
    geolocator = None

# Tamil Nadu travel guide system prompt
SYSTEM_PROMPT = """You are "Vaanga Suthalam" (வாங்க சுத்தலாம்), a friendly Tamil Nadu travel and agriculture guide chatbot specifically designed to help foreign tourists and agricultural enthusiasts explore Tamil Nadu, India. Your name means "Come, let's travel" in Tamil.

Your expertise includes:

TRAVEL GUIDANCE:
- Popular destinations (Chennai, Madurai, Kanyakumari, Ooty, Kodaikanal, Pondicherry, Thanjavur, etc.)
- Cultural sites (temples, palaces, museums)
- Local cuisine and food recommendations
- Transportation options (trains, buses, taxis, auto-rickshaws)
- Accommodation suggestions for different budgets
- Cultural etiquette and customs
- Weather and best times to visit
- Local festivals and events
- Shopping areas and markets
- Safety tips for foreign travelers
- Basic Tamil phrases that might be helpful

AGRICULTURAL EXPERTISE:
- Tamil Nadu's major crops (rice, sugarcane, cotton, groundnut, coconut, etc.)
- Traditional farming practices and modern techniques
- Agricultural seasons and crop calendars
- Plant diseases and pest management
- Soil types and agricultural zones in Tamil Nadu
- Agricultural tourism and farm visits
- Organic farming practices
- Agricultural markets and cooperatives
- Government schemes for farmers
- Agricultural research institutions in Tamil Nadu

Always provide:
- Practical, actionable advice
- Budget-friendly and luxury options when relevant
- Cultural context to enhance understanding
- Safety considerations
- Local insights that guidebooks might miss
- Scientific and practical agricultural information
- Sustainable farming recommendations

Be warm, welcoming, and enthusiastic about Tamil Nadu's rich culture, heritage, and agricultural traditions. Use simple English suitable for international visitors and agricultural enthusiasts."""

# Agricultural image analysis system prompt
AGRI_VISION_PROMPT = """You are an expert agricultural consultant specializing in Tamil Nadu's agriculture. Analyze the provided image of a plant, leaf, tree, or crop and provide a comprehensive report.

Your analysis should include:

1. PLANT IDENTIFICATION:
   - Scientific name and common names (English and Tamil if applicable)
   - Plant family and characteristics
   - Whether it's commonly grown in Tamil Nadu

2. HEALTH ASSESSMENT:
   - Overall plant health status
   - Any visible diseases, pests, or nutrient deficiencies
   - Signs of stress (water, heat, nutrient, etc.)

3. AGRICULTURAL INSIGHTS:
   - Growing conditions and requirements
   - Best practices for cultivation in Tamil Nadu climate
   - Suitable seasons for planting/harvesting
   - Common challenges and solutions

4. DISEASE/PEST MANAGEMENT (if applicable):
   - Specific disease or pest identification
   - Organic and chemical treatment options
   - Prevention strategies
   - Expected recovery timeline

5. RECOMMENDATIONS:
   - Immediate actions needed
   - Long-term care suggestions
   - Fertilizer or nutrient recommendations
   - Irrigation and care tips

6. TAMIL NADU CONTEXT:
   - How this crop fits into local agriculture
   - Market value and commercial viability
   - Government schemes or support available
   - Local agricultural practices

Provide practical, actionable advice that farmers and agricultural enthusiasts can implement. Be specific about treatments, timelines, and expected outcomes."""

# Tree and Plant Identification System Prompt
TREE_PLANT_ID_PROMPT = """You are an expert botanist and dendrologist specializing in Tamil Nadu's flora, including trees, shrubs, herbs, and all plant species. Analyze the provided plant/tree/leaf image and provide comprehensive identification and information.

Your analysis should include:

1. PLANT/TREE IDENTIFICATION:
   - Scientific name and botanical classification
   - Common names in English and Tamil
   - Plant family and genus
   - Type (tree, shrub, herb, climber, etc.)
   - Native or introduced species status in Tamil Nadu

2. PHYSICAL CHARACTERISTICS:
   - Leaf shape, size, arrangement, and venation
   - Bark texture and color (if visible)
   - Flower and fruit characteristics (if applicable)
   - Growth habit and mature size
   - Distinctive identifying features

3. HABITAT AND DISTRIBUTION:
   - Natural habitat preferences
   - Distribution across Tamil Nadu districts
   - Elevation and climate requirements
   - Soil preferences
   - Associated plant communities

4. ECOLOGICAL IMPORTANCE:
   - Role in local ecosystems
   - Wildlife relationships (birds, insects, mammals)
   - Pollination and seed dispersal mechanisms
   - Soil conservation and environmental benefits
   - Carbon sequestration potential

5. HUMAN USES AND BENEFITS:
   - Traditional and modern uses
   - Timber, fuel, or construction applications
   - Medicinal properties (if any)
   - Food uses (fruits, leaves, etc.)
   - Cultural and religious significance in Tamil culture

6. CULTIVATION AND CARE:
   - Growing requirements and conditions
   - Propagation methods
   - Maintenance and care tips
   - Common pests and diseases
   - Best planting seasons in Tamil Nadu

7. CONSERVATION STATUS:
   - Current conservation status
   - Threats and challenges
   - Conservation efforts needed
   - Sustainable use practices

Always provide accurate scientific information with practical insights for identification, cultivation, and conservation."""

# Comprehensive Tamil Nadu Tourist Guide System Prompt
TOURIST_GUIDE_PROMPT = """You are "Vaanga Suthalam" (வாங்க சுத்தலாம்), the ultimate Tamil Nadu tourist guide with comprehensive knowledge of every district, city, town, and attraction across the state. You provide detailed, practical, and culturally rich travel guidance.

Your expertise covers ALL 38 DISTRICTS of Tamil Nadu:

NORTHERN DISTRICTS:
- Chennai, Tiruvallur, Kanchipuram, Chengalpattu, Ranipet, Tirupattur, Vellore, Tiruvannamalai

WESTERN DISTRICTS:
- Dharmapuri, Krishnagiri, Salem, Namakkal, Erode, Tiruppur, Coimbatore, Nilgiris

CENTRAL DISTRICTS:
- Karur, Dindigul, Theni, Madurai, Sivaganga, Pudukkottai, Thanjavur, Tiruvarur, Nagapattinam

SOUTHERN DISTRICTS:
- Virudhunagar, Ramanathapuram, Thoothukudi, Tirunelveli, Tenkasi, Kanyakumari

EASTERN DISTRICTS:
- Cuddalore, Villupuram, Kallakurichi, Ariyalur, Perambalur, Tiruchirappalli, Karaikal (Puducherry)

For each location, provide:

1. ATTRACTIONS & SIGHTSEEING:
   - Temples, monuments, and heritage sites
   - Natural attractions (hills, beaches, waterfalls, forests)
   - Museums, art galleries, and cultural centers
   - Unique local experiences and hidden gems

2. CULTURAL INSIGHTS:
   - Local history and legends
   - Festivals and celebrations
   - Traditional arts, crafts, and performances
   - Religious significance and practices

3. PRACTICAL TRAVEL INFORMATION:
   - Best time to visit and weather patterns
   - How to reach (by air, rail, road)
   - Local transportation options
   - Accommodation (budget to luxury)

4. FOOD & CUISINE:
   - Regional specialties and must-try dishes
   - Famous restaurants and street food spots
   - Local markets and food experiences
   - Vegetarian and non-vegetarian options

5. SHOPPING & SOUVENIRS:
   - Local handicrafts and specialties
   - Traditional textiles and jewelry
   - Spices, sweets, and local products
   - Best shopping areas and markets

6. ITINERARY SUGGESTIONS:
   - Day-wise travel plans
   - Multi-city tour combinations
   - Duration recommendations
   - Budget planning and cost estimates

7. SAFETY & TIPS:
   - Local customs and etiquette
   - Safety precautions and guidelines
   - Language tips and useful Tamil phrases
   - Emergency contacts and services

Always provide current, accurate information with insider tips that make the travel experience authentic and memorable."""

# Mock responses for when OpenAI is not available
MOCK_RESPONSES = {
    'default': """வணக்கம்! (Vanakkam!) Welcome to Tamil Nadu! I'm your AI travel companion for exploring this incredible state.

🏛️ **Popular Destinations:**
- Chennai: Marina Beach, Kapaleeshwarar Temple, Fort St. George
- Madurai: Meenakshi Temple, Thirumalai Nayakkar Palace
- Kanyakumari: Vivekananda Rock Memorial, Thiruvalluvar Statue
- Ooty: Botanical Gardens, Doddabetta Peak, Toy Train
- Thanjavur: Brihadeeswarar Temple (UNESCO World Heritage)

🍛 **Must-Try Foods:**
- Idli, Dosa, Sambar, Rasam
- Chettinad Chicken, Fish Curry
- Filter Coffee, Payasam

🚂 **Transportation:**
- Extensive railway network
- State transport buses
- Auto-rickshaws and taxis
- Domestic airports in major cities

Feel free to ask me about specific places, food, culture, or travel tips!""",

    'tree_identification': """🌳 **Tree/Plant Identification Service**

I can help identify trees and plants found in Tamil Nadu! Based on your image, here's what I can tell you:

**Common Tamil Nadu Trees:**
- Neem (வேம்பு - Vembu): Medicinal properties, pest control
- Banyan (ஆல் - Aal): Sacred tree, provides shade
- Coconut Palm (தென்னை - Thennai): Versatile uses, coastal regions
- Tamarind (புளி - Puli): Culinary uses, traditional medicine
- Mango (மாம்பழம் - Maampazham): King of fruits

**For accurate identification:**
1. Upload a clear image of leaves, bark, or flowers
2. Mention the location where you found it
3. Note any special characteristics

I'll provide Tamil names, uses, and cultural significance!""",

    'tourist_guide': """🗺️ **Tamil Nadu Complete District Guide**

**Northern Region:**
- Chennai: Capital city, Marina Beach, cultural hub
- Kanchipuram: Silk sarees, ancient temples
- Vellore: Fort, medical college, leather industry

**Central Region:**
- Madurai: Temple city, cultural capital
- Thanjavur: Art, music, Chola heritage
- Tiruchirappalli: Rock Fort, temples

**Southern Region:**
- Kanyakumari: Southernmost tip, sunrise/sunset views
- Tirunelveli: Halwa, temples, Thamirabarani river

**Western Region:**
- Coimbatore: Manchester of South India
- Nilgiris: Hill station, tea gardens
- Salem: Steel city, mango hub

**Eastern Region:**
- Cuddalore: Port city, SIPCOT industries
- Villupuram: Railway junction, temples

Each district has unique attractions, cuisine, and cultural heritage!""",

    'itinerary': """📋 **Sample 7-Day Tamil Nadu Itinerary**

**Day 1-2: Chennai**
- Marina Beach, Kapaleeshwarar Temple
- Government Museum, Fort St. George
- Local food tour in T. Nagar

**Day 3: Kanchipuram (Day trip)**
- Kailasanathar Temple, Ekambareswarar Temple
- Silk saree shopping
- Return to Chennai

**Day 4-5: Madurai**
- Meenakshi Amman Temple
- Thirumalai Nayakkar Palace
- Gandhi Memorial Museum
- Local Chettinad cuisine

**Day 6-7: Kanyakumari**
- Vivekananda Rock Memorial
- Thiruvalluvar Statue
- Sunset/Sunrise viewing
- Padmanabhapuram Palace

**Budget:** ₹15,000-25,000 per person
**Best Time:** October to March
**Transportation:** Train + local transport

Customize based on your interests!"""
}

def get_mock_response(query_type='default'):
    return MOCK_RESPONSES.get(query_type, MOCK_RESPONSES['default'])

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/chat', methods=['POST'])
def chat():
    try:
        user_message = request.json.get('message', '')
        
        if not user_message:
            return jsonify({'error': 'No message provided'}), 400
        
        # Create chat completion with OpenAI
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": SYSTEM_PROMPT},
                {"role": "user", "content": user_message}
            ],
            max_tokens=500,
            temperature=0.7
        )

        bot_response = response.choices[0].message.content.strip()
        
        return jsonify({
            'response': bot_response,
            'status': 'success'
        })
        
    except Exception as e:
        return jsonify({
            'error': f'An error occurred: {str(e)}',
            'status': 'error'
        }), 500

@app.route('/analyze-image', methods=['POST'])
def analyze_image():
    try:
        # Check if image file is present
        if 'image' not in request.files:
            return jsonify({'error': 'No image file provided'}), 400

        file = request.files['image']
        if file.filename == '':
            return jsonify({'error': 'No image file selected'}), 400

        # Validate file type
        allowed_extensions = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'}
        file_extension = file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else ''

        if file_extension not in allowed_extensions:
            return jsonify({'error': 'Invalid file type. Please upload an image file.'}), 400

        # Read and process the image
        image_data = file.read()

        # Convert image to base64
        image_base64 = base64.b64encode(image_data).decode('utf-8')

        # Prepare the image for OpenAI Vision API
        image_url = f"data:image/{file_extension};base64,{image_base64}"

        # Create chat completion with vision
        response = client.chat.completions.create(
            model="gpt-4-vision-preview",
            messages=[
                {
                    "role": "system",
                    "content": AGRI_VISION_PROMPT
                },
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "Please analyze this agricultural image and provide a comprehensive report about the plant, its health, and recommendations for Tamil Nadu farming conditions."
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": image_url,
                                "detail": "high"
                            }
                        }
                    ]
                }
            ],
            max_tokens=1000,
            temperature=0.3
        )

        analysis_result = response.choices[0].message.content.strip()

        return jsonify({
            'analysis': analysis_result,
            'status': 'success',
            'image_processed': True
        })

    except Exception as e:
        return jsonify({
            'error': f'Error analyzing image: {str(e)}',
            'status': 'error'
        }), 500

@app.route('/chat-with-image', methods=['POST'])
def chat_with_image():
    try:
        # Get both message and image
        user_message = request.form.get('message', '')

        if 'image' not in request.files:
            return jsonify({'error': 'No image file provided'}), 400

        file = request.files['image']
        if file.filename == '':
            return jsonify({'error': 'No image file selected'}), 400

        # Process image
        image_data = file.read()
        image_base64 = base64.b64encode(image_data).decode('utf-8')
        file_extension = file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else 'jpg'
        image_url = f"data:image/{file_extension};base64,{image_base64}"

        # Create chat completion with both text and image
        messages = [
            {"role": "system", "content": SYSTEM_PROMPT}
        ]

        # Add user message with image
        user_content = [
            {
                "type": "text",
                "text": user_message if user_message else "Please analyze this agricultural image and tell me about it in the context of Tamil Nadu farming."
            },
            {
                "type": "image_url",
                "image_url": {
                    "url": image_url,
                    "detail": "high"
                }
            }
        ]

        messages.append({
            "role": "user",
            "content": user_content
        })

        response = client.chat.completions.create(
            model="gpt-4-vision-preview",
            messages=messages,
            max_tokens=800,
            temperature=0.5
        )

        bot_response = response.choices[0].message.content.strip()

        return jsonify({
            'response': bot_response,
            'status': 'success',
            'has_image': True
        })

    except Exception as e:
        return jsonify({
            'error': f'Error processing chat with image: {str(e)}',
            'status': 'error'
        }), 500

@app.route('/identify-tree-plant', methods=['POST'])
def identify_tree_plant():
    try:
        # Check if image file is present
        if 'image' not in request.files:
            return jsonify({'error': 'No image file provided'}), 400

        file = request.files['image']
        if file.filename == '':
            return jsonify({'error': 'No image file selected'}), 400

        # Validate file type
        allowed_extensions = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'}
        file_extension = file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else ''

        if file_extension not in allowed_extensions:
            return jsonify({'error': 'Invalid file type. Please upload an image file.'}), 400

        # Read and process the image
        image_data = file.read()

        # Convert image to base64
        image_base64 = base64.b64encode(image_data).decode('utf-8')

        # Prepare the image for OpenAI Vision API
        image_url = f"data:image/{file_extension};base64,{image_base64}"

        # Create chat completion with vision for tree/plant identification
        response = client.chat.completions.create(
            model="gpt-4-vision-preview",
            messages=[
                {
                    "role": "system",
                    "content": TREE_PLANT_ID_PROMPT
                },
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "Please identify this tree, plant, or leaf and provide comprehensive information about its characteristics, habitat, uses, and significance in Tamil Nadu."
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": image_url,
                                "detail": "high"
                            }
                        }
                    ]
                }
            ],
            max_tokens=1500,
            temperature=0.2
        )

        identification_result = response.choices[0].message.content.strip()

        return jsonify({
            'identification': identification_result,
            'status': 'success',
            'analysis_type': 'tree_plant_identification',
            'image_processed': True
        })

    except Exception as e:
        return jsonify({
            'error': f'Error identifying tree/plant: {str(e)}',
            'status': 'error'
        }), 500

@app.route('/tourist-guide', methods=['POST'])
def tourist_guide():
    try:
        data = request.get_json()
        location = data.get('location', '')
        query_type = data.get('type', 'general')  # general, attractions, food, accommodation, etc.
        user_message = data.get('message', '')

        if not location and not user_message:
            return jsonify({'error': 'Location or message required'}), 400

        # Construct the query based on type and location
        if location and not user_message:
            query_messages = {
                'general': f"Provide a comprehensive travel guide for {location} in Tamil Nadu. Include attractions, culture, food, accommodation, and practical travel tips.",
                'attractions': f"What are the top attractions and must-visit places in {location}, Tamil Nadu? Include temples, monuments, natural sites, and unique experiences.",
                'food': f"What are the famous local dishes and food experiences in {location}, Tamil Nadu? Include restaurants, street food, and regional specialties.",
                'accommodation': f"What are the best accommodation options in {location}, Tamil Nadu? Include budget, mid-range, and luxury options with practical booking tips.",
                'transport': f"How do I travel to and around {location} in Tamil Nadu? Include all transportation options, routes, and local travel tips.",
                'culture': f"Tell me about the culture, history, festivals, and traditions of {location} in Tamil Nadu. Include local customs and cultural experiences.",
                'shopping': f"What are the best shopping experiences and local products to buy in {location}, Tamil Nadu? Include markets, handicrafts, and souvenirs."
            }

            final_message = query_messages.get(query_type, query_messages['general'])
        else:
            final_message = user_message

        # Create chat completion with tourist guide prompt
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": TOURIST_GUIDE_PROMPT},
                {"role": "user", "content": final_message}
            ],
            max_tokens=1200,
            temperature=0.6
        )

        guide_response = response.choices[0].message.content.strip()

        return jsonify({
            'response': guide_response,
            'location': location,
            'query_type': query_type,
            'status': 'success'
        })

    except Exception as e:
        return jsonify({
            'error': f'Error in tourist guide: {str(e)}',
            'status': 'error'
        }), 500

@app.route('/district-info/<district_name>')
def get_district_info(district_name):
    try:
        # Comprehensive district information
        district_query = f"""Provide comprehensive information about {district_name} district in Tamil Nadu. Include:
        1. Overview and geography
        2. Major cities and towns
        3. Top tourist attractions
        4. Cultural significance and history
        5. Famous temples and monuments
        6. Local cuisine and specialties
        7. Festivals and events
        8. Transportation and connectivity
        9. Best time to visit
        10. Accommodation options

        Make it detailed and practical for tourists."""

        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": TOURIST_GUIDE_PROMPT},
                {"role": "user", "content": district_query}
            ],
            max_tokens=1500,
            temperature=0.5
        )

        district_info = response.choices[0].message.content.strip()

        return jsonify({
            'district': district_name,
            'information': district_info,
            'status': 'success'
        })

    except Exception as e:
        return jsonify({
            'error': f'Error getting district information: {str(e)}',
            'status': 'error'
        }), 500

@app.route('/travel-itinerary', methods=['POST'])
def create_travel_itinerary():
    try:
        data = request.get_json()
        destinations = data.get('destinations', [])
        duration = data.get('duration', 7)  # days
        budget = data.get('budget', 'medium')  # low, medium, high
        interests = data.get('interests', [])  # temples, nature, culture, food, etc.

        if not destinations:
            return jsonify({'error': 'At least one destination required'}), 400

        itinerary_query = f"""Create a detailed {duration}-day travel itinerary for Tamil Nadu covering these destinations: {', '.join(destinations)}.

        Budget level: {budget}
        Interests: {', '.join(interests) if interests else 'general sightseeing'}

        Please provide:
        1. Day-wise detailed itinerary
        2. Transportation between cities
        3. Accommodation recommendations for each location
        4. Must-visit attractions and experiences
        5. Local food recommendations
        6. Estimated costs and budget breakdown
        7. Best routes and travel tips
        8. Cultural insights and local customs
        9. Packing suggestions and weather considerations
        10. Emergency contacts and important information

        Make it practical, detailed, and culturally enriching."""

        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": TOURIST_GUIDE_PROMPT},
                {"role": "user", "content": itinerary_query}
            ],
            max_tokens=2000,
            temperature=0.6
        )

        itinerary = response.choices[0].message.content.strip()

        return jsonify({
            'itinerary': itinerary,
            'destinations': destinations,
            'duration': duration,
            'budget': budget,
            'interests': interests,
            'status': 'success'
        })

    except Exception as e:
        return jsonify({
            'error': f'Error creating itinerary: {str(e)}',
            'status': 'error'
        }), 500

@app.route('/health')
def health():
    return jsonify({'status': 'healthy', 'service': 'Vaanga Suthalam - Complete Tamil Nadu Guide'})

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
