from flask import Flask, request, jsonify, render_template
from flask_cors import CORS
from openai import OpenAI
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

app = Flask(__name__)
CORS(app)

# Configure OpenAI
client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))

# Tamil Nadu travel guide system prompt
SYSTEM_PROMPT = """You are "Vaanga Suthalam" (வாங்க சுத்தலாம்), a friendly Tamil Nadu travel guide chatbot specifically designed to help foreign tourists explore Tamil Nadu, India. Your name means "Come, let's travel" in Tamil.

Your expertise includes:
- Popular destinations (Chennai, Madurai, Kanyakumari, Ooty, Kodaikanal, Pondicherry, Thanjavur, etc.)
- Cultural sites (temples, palaces, museums)
- Local cuisine and food recommendations
- Transportation options (trains, buses, taxis, auto-rickshaws)
- Accommodation suggestions for different budgets
- Cultural etiquette and customs
- Weather and best times to visit
- Local festivals and events
- Shopping areas and markets
- Safety tips for foreign travelers
- Basic Tamil phrases that might be helpful

Always provide:
- Practical, actionable advice
- Budget-friendly and luxury options when relevant
- Cultural context to enhance understanding
- Safety considerations
- Local insights that guidebooks might miss

Be warm, welcoming, and enthusiastic about Tamil Nadu's rich culture and heritage. Use simple English suitable for international visitors."""

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/chat', methods=['POST'])
def chat():
    try:
        user_message = request.json.get('message', '')
        
        if not user_message:
            return jsonify({'error': 'No message provided'}), 400
        
        # Create chat completion with OpenAI
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": SYSTEM_PROMPT},
                {"role": "user", "content": user_message}
            ],
            max_tokens=500,
            temperature=0.7
        )

        bot_response = response.choices[0].message.content.strip()
        
        return jsonify({
            'response': bot_response,
            'status': 'success'
        })
        
    except Exception as e:
        return jsonify({
            'error': f'An error occurred: {str(e)}',
            'status': 'error'
        }), 500

@app.route('/health')
def health():
    return jsonify({'status': 'healthy', 'service': 'Vaanga Suthalam Travel Bot'})

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
