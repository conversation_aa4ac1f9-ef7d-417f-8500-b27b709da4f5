from flask import Flask, request, jsonify, render_template
from flask_cors import CORS
from openai import OpenAI
import os
import base64
import io
from PIL import Image
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

app = Flask(__name__)
CORS(app)

# Configure OpenAI
client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))

# Tamil Nadu travel guide system prompt
SYSTEM_PROMPT = """You are "Vaanga Suthalam" (வாங்க சுத்தலாம்), a friendly Tamil Nadu travel and agriculture guide chatbot specifically designed to help foreign tourists and agricultural enthusiasts explore Tamil Nadu, India. Your name means "Come, let's travel" in Tamil.

Your expertise includes:

TRAVEL GUIDANCE:
- Popular destinations (Chennai, Madurai, Kanyakumari, Ooty, Kodaikanal, Pondicherry, Thanjavur, etc.)
- Cultural sites (temples, palaces, museums)
- Local cuisine and food recommendations
- Transportation options (trains, buses, taxis, auto-rickshaws)
- Accommodation suggestions for different budgets
- Cultural etiquette and customs
- Weather and best times to visit
- Local festivals and events
- Shopping areas and markets
- Safety tips for foreign travelers
- Basic Tamil phrases that might be helpful

AGRICULTURAL EXPERTISE:
- Tamil Nadu's major crops (rice, sugarcane, cotton, groundnut, coconut, etc.)
- Traditional farming practices and modern techniques
- Agricultural seasons and crop calendars
- Plant diseases and pest management
- Soil types and agricultural zones in Tamil Nadu
- Agricultural tourism and farm visits
- Organic farming practices
- Agricultural markets and cooperatives
- Government schemes for farmers
- Agricultural research institutions in Tamil Nadu

Always provide:
- Practical, actionable advice
- Budget-friendly and luxury options when relevant
- Cultural context to enhance understanding
- Safety considerations
- Local insights that guidebooks might miss
- Scientific and practical agricultural information
- Sustainable farming recommendations

Be warm, welcoming, and enthusiastic about Tamil Nadu's rich culture, heritage, and agricultural traditions. Use simple English suitable for international visitors and agricultural enthusiasts."""

# Agricultural image analysis system prompt
AGRI_VISION_PROMPT = """You are an expert agricultural consultant specializing in Tamil Nadu's agriculture. Analyze the provided image of a plant, leaf, tree, or crop and provide a comprehensive report.

Your analysis should include:

1. PLANT IDENTIFICATION:
   - Scientific name and common names (English and Tamil if applicable)
   - Plant family and characteristics
   - Whether it's commonly grown in Tamil Nadu

2. HEALTH ASSESSMENT:
   - Overall plant health status
   - Any visible diseases, pests, or nutrient deficiencies
   - Signs of stress (water, heat, nutrient, etc.)

3. AGRICULTURAL INSIGHTS:
   - Growing conditions and requirements
   - Best practices for cultivation in Tamil Nadu climate
   - Suitable seasons for planting/harvesting
   - Common challenges and solutions

4. DISEASE/PEST MANAGEMENT (if applicable):
   - Specific disease or pest identification
   - Organic and chemical treatment options
   - Prevention strategies
   - Expected recovery timeline

5. RECOMMENDATIONS:
   - Immediate actions needed
   - Long-term care suggestions
   - Fertilizer or nutrient recommendations
   - Irrigation and care tips

6. TAMIL NADU CONTEXT:
   - How this crop fits into local agriculture
   - Market value and commercial viability
   - Government schemes or support available
   - Local agricultural practices

Provide practical, actionable advice that farmers and agricultural enthusiasts can implement. Be specific about treatments, timelines, and expected outcomes."""

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/chat', methods=['POST'])
def chat():
    try:
        user_message = request.json.get('message', '')
        
        if not user_message:
            return jsonify({'error': 'No message provided'}), 400
        
        # Create chat completion with OpenAI
        response = client.chat.completions.create(
            model="gpt-3.5-turbo",
            messages=[
                {"role": "system", "content": SYSTEM_PROMPT},
                {"role": "user", "content": user_message}
            ],
            max_tokens=500,
            temperature=0.7
        )

        bot_response = response.choices[0].message.content.strip()
        
        return jsonify({
            'response': bot_response,
            'status': 'success'
        })
        
    except Exception as e:
        return jsonify({
            'error': f'An error occurred: {str(e)}',
            'status': 'error'
        }), 500

@app.route('/analyze-image', methods=['POST'])
def analyze_image():
    try:
        # Check if image file is present
        if 'image' not in request.files:
            return jsonify({'error': 'No image file provided'}), 400

        file = request.files['image']
        if file.filename == '':
            return jsonify({'error': 'No image file selected'}), 400

        # Validate file type
        allowed_extensions = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'}
        file_extension = file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else ''

        if file_extension not in allowed_extensions:
            return jsonify({'error': 'Invalid file type. Please upload an image file.'}), 400

        # Read and process the image
        image_data = file.read()

        # Convert image to base64
        image_base64 = base64.b64encode(image_data).decode('utf-8')

        # Prepare the image for OpenAI Vision API
        image_url = f"data:image/{file_extension};base64,{image_base64}"

        # Create chat completion with vision
        response = client.chat.completions.create(
            model="gpt-4-vision-preview",
            messages=[
                {
                    "role": "system",
                    "content": AGRI_VISION_PROMPT
                },
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "Please analyze this agricultural image and provide a comprehensive report about the plant, its health, and recommendations for Tamil Nadu farming conditions."
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": image_url,
                                "detail": "high"
                            }
                        }
                    ]
                }
            ],
            max_tokens=1000,
            temperature=0.3
        )

        analysis_result = response.choices[0].message.content.strip()

        return jsonify({
            'analysis': analysis_result,
            'status': 'success',
            'image_processed': True
        })

    except Exception as e:
        return jsonify({
            'error': f'Error analyzing image: {str(e)}',
            'status': 'error'
        }), 500

@app.route('/chat-with-image', methods=['POST'])
def chat_with_image():
    try:
        # Get both message and image
        user_message = request.form.get('message', '')

        if 'image' not in request.files:
            return jsonify({'error': 'No image file provided'}), 400

        file = request.files['image']
        if file.filename == '':
            return jsonify({'error': 'No image file selected'}), 400

        # Process image
        image_data = file.read()
        image_base64 = base64.b64encode(image_data).decode('utf-8')
        file_extension = file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else 'jpg'
        image_url = f"data:image/{file_extension};base64,{image_base64}"

        # Create chat completion with both text and image
        messages = [
            {"role": "system", "content": SYSTEM_PROMPT}
        ]

        # Add user message with image
        user_content = [
            {
                "type": "text",
                "text": user_message if user_message else "Please analyze this agricultural image and tell me about it in the context of Tamil Nadu farming."
            },
            {
                "type": "image_url",
                "image_url": {
                    "url": image_url,
                    "detail": "high"
                }
            }
        ]

        messages.append({
            "role": "user",
            "content": user_content
        })

        response = client.chat.completions.create(
            model="gpt-4-vision-preview",
            messages=messages,
            max_tokens=800,
            temperature=0.5
        )

        bot_response = response.choices[0].message.content.strip()

        return jsonify({
            'response': bot_response,
            'status': 'success',
            'has_image': True
        })

    except Exception as e:
        return jsonify({
            'error': f'Error processing chat with image: {str(e)}',
            'status': 'error'
        }), 500

@app.route('/health')
def health():
    return jsonify({'status': 'healthy', 'service': 'Vaanga Suthalam Travel & Agriculture Bot'})

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
