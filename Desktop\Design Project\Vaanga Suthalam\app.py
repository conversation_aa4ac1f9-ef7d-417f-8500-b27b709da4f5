from flask import Flask, request, jsonify, render_template
from flask_cors import CORS
import os
import base64
import io
import json
import threading
import time
from PIL import Image
from dotenv import load_dotenv

# Try to import local model dependencies
try:
    from llama_cpp import Llama
    LOCAL_MODEL_AVAILABLE = True
    print("✅ Local model support available (llama-cpp-python)")
except ImportError:
    LOCAL_MODEL_AVAILABLE = False
    print("⚠️  llama-cpp-python not available - running with fallback responses")
    print("💡 To install: pip install llama-cpp-python (requires C++ build tools)")

    # Create a dummy Llama class for fallback
    class Llama:
        def __init__(self, *args, **kwargs):
            pass
        def __call__(self, *args, **kwargs):
            return {'choices': [{'text': 'Local model not available'}]}

try:
    from flask_sqlalchemy import SQLAlchemy
    SQLALCHEMY_AVAILABLE = True
except ImportError:
    SQLALCHEMY_AVAILABLE = False
    print("SQLAlchemy not available - running without database")

try:
    from geopy.distance import geodesic
    from geopy.geocoders import Nominatim
    GEOPY_AVAILABLE = True
except ImportError:
    GEOPY_AVAILABLE = False
    print("Geopy not available - location features limited")

# Load environment variables
load_dotenv()

app = Flask(__name__)
CORS(app)

# Configure database if available
if SQLALCHEMY_AVAILABLE:
    app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///vaanga_suthalam.db'
    app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
    db = SQLAlchemy(app)

# Configure Local Model
local_model = None
model_lock = threading.Lock()

def load_local_model():
    """Load the local GGUF model"""
    global local_model

    if not LOCAL_MODEL_AVAILABLE:
        print("⚠️  Cannot load local model - llama-cpp-python not available")
        print("🔄 Running with intelligent fallback responses")
        local_model = None
        return False

    try:
        # Path to your local model
        model_path = os.path.join(os.path.dirname(__file__),
                                 "models", "bartowski", "Llama-3.2-3B-Instruct-GGUF",
                                 "Llama-3.2-3B-Instruct-Q4_K_S.gguf")

        if not os.path.exists(model_path):
            print(f"⚠️  Model file not found: {model_path}")
            print("🔄 Running with intelligent fallback responses")
            local_model = None
            return False

        print(f"🔄 Loading local model from: {model_path}")

        # Initialize the model with optimized settings
        local_model = Llama(
            model_path=model_path,
            n_ctx=4096,  # Context window
            n_threads=4,  # Number of threads
            n_gpu_layers=0,  # Use CPU only (set to -1 for GPU if available)
            verbose=False,
            chat_format="llama-2"  # Chat format for instruction following
        )

        print("✅ Local model loaded successfully!")
        return True

    except Exception as e:
        print(f"⚠️  Error loading local model: {str(e)}")
        print("🔄 Running with intelligent fallback responses")
        local_model = None
        return False

def generate_local_response(prompt, max_tokens=1500, temperature=0.3):
    """Generate response using local model with fallback"""
    global local_model

    if not local_model:
        # Provide fallback response instead of error
        return get_fallback_response(prompt)

    try:
        with model_lock:
            response = local_model(
                prompt,
                max_tokens=max_tokens,
                temperature=temperature,
                top_p=0.9,
                repeat_penalty=1.1,
                stop=["</s>", "Human:", "Assistant:", "<|eot_id|>"],
                echo=False
            )

            return response['choices'][0]['text'].strip()

    except Exception as e:
        print(f"❌ Error generating response: {str(e)}")
        # Return fallback instead of error message
        return get_fallback_response(prompt)

def get_fallback_response(prompt):
    """Provide fallback responses when local model is not available"""

    # Extract user message from prompt for context
    if "user<|end_header_id|>" in prompt:
        user_part = prompt.split("user<|end_header_id|>")[-1].split("<|eot_id|>")[0].strip()
    else:
        user_part = prompt.lower()

    # Tamil Nadu tourism responses
    if any(word in user_part.lower() for word in ['chennai', 'madurai', 'temple', 'tourism', 'travel', 'visit']):
        return """🏛️ **Tamil Nadu Tourism Information**

**Popular Destinations:**
- **Chennai**: Marina Beach, Kapaleeshwarar Temple, Fort St. George
- **Madurai**: Meenakshi Amman Temple, Thirumalai Nayakkar Palace
- **Kanyakumari**: Vivekananda Rock Memorial, Thiruvalluvar Statue
- **Ooty**: Botanical Gardens, Doddabetta Peak, Toy Train

**Best Time to Visit:** October to March
**Languages:** Tamil, English widely spoken
**Currency:** Indian Rupee (₹)

For detailed AI-powered responses, please ensure the local model is properly loaded."""

    # Plant/agriculture responses
    elif any(word in user_part.lower() for word in ['plant', 'tree', 'agriculture', 'crop', 'farming']):
        return """🌱 **Tamil Nadu Agriculture & Plants**

**Major Crops:**
- **Rice**: Primary crop, grown in delta regions
- **Sugarcane**: Major cash crop
- **Cotton**: Important in western districts
- **Coconut**: Coastal areas specialty

**Traditional Plants:**
- **Neem**: Medicinal properties, pest control
- **Banyan**: Sacred tree, provides shade
- **Tamarind**: Culinary uses, traditional medicine

**Climate:** Tropical, suitable for diverse crops
**Seasons:** Kharif (June-Oct), Rabi (Nov-Feb)

For detailed plant identification, please ensure the local AI model is loaded."""

    # General fallback
    else:
        return """வணக்கம்! (Vanakkam!) Welcome to Tamil Nadu!

I'm your Tamil Nadu guide, but I'm currently running in basic mode. For full AI-powered assistance, please:

1. **Check Model Loading**: Ensure the local AI model is properly loaded
2. **Restart Application**: Try restarting the app
3. **Check Resources**: Ensure sufficient RAM (4-8GB recommended)

**Quick Tamil Nadu Facts:**
- 🏛️ **Temples**: Over 40,000 temples
- 🍛 **Cuisine**: Famous for dosa, idli, sambar
- 🎭 **Culture**: Rich classical music and dance traditions
- 🌊 **Coastline**: 1,000+ km of beautiful beaches

For detailed, personalized assistance, please resolve the model loading issue."""

# Initialize geocoder if available
if GEOPY_AVAILABLE:
    geolocator = Nominatim(user_agent="vaanga_suthalam")
else:
    geolocator = None

# Load the local model on startup
print("🚀 Initializing Vaanga Suthalam with Local AI Model...")
model_loaded = load_local_model()

# Tamil Nadu travel guide system prompt
SYSTEM_PROMPT = """You are "Vaanga Suthalam" (வாங்க சுத்தலாம்), a friendly Tamil Nadu travel and agriculture guide chatbot specifically designed to help foreign tourists and agricultural enthusiasts explore Tamil Nadu, India. Your name means "Come, let's travel" in Tamil.

Your expertise includes:

TRAVEL GUIDANCE:
- Popular destinations (Chennai, Madurai, Kanyakumari, Ooty, Kodaikanal, Pondicherry, Thanjavur, etc.)
- Cultural sites (temples, palaces, museums)
- Local cuisine and food recommendations
- Transportation options (trains, buses, taxis, auto-rickshaws)
- Accommodation suggestions for different budgets
- Cultural etiquette and customs
- Weather and best times to visit
- Local festivals and events
- Shopping areas and markets
- Safety tips for foreign travelers
- Basic Tamil phrases that might be helpful

AGRICULTURAL EXPERTISE:
- Tamil Nadu's major crops (rice, sugarcane, cotton, groundnut, coconut, etc.)
- Traditional farming practices and modern techniques
- Agricultural seasons and crop calendars
- Plant diseases and pest management
- Soil types and agricultural zones in Tamil Nadu
- Agricultural tourism and farm visits
- Organic farming practices
- Agricultural markets and cooperatives
- Government schemes for farmers
- Agricultural research institutions in Tamil Nadu

Always provide:
- Practical, actionable advice
- Budget-friendly and luxury options when relevant
- Cultural context to enhance understanding
- Safety considerations
- Local insights that guidebooks might miss
- Scientific and practical agricultural information
- Sustainable farming recommendations

Be warm, welcoming, and enthusiastic about Tamil Nadu's rich culture, heritage, and agricultural traditions. Use simple English suitable for international visitors and agricultural enthusiasts."""

# Agricultural image analysis system prompt
AGRI_VISION_PROMPT = """You are an expert agricultural consultant specializing in Tamil Nadu's agriculture. Analyze the provided image of a plant, leaf, tree, or crop and provide a comprehensive report.

Your analysis should include:

1. PLANT IDENTIFICATION:
   - Scientific name and common names (English and Tamil if applicable)
   - Plant family and characteristics
   - Whether it's commonly grown in Tamil Nadu

2. HEALTH ASSESSMENT:
   - Overall plant health status
   - Any visible diseases, pests, or nutrient deficiencies
   - Signs of stress (water, heat, nutrient, etc.)

3. AGRICULTURAL INSIGHTS:
   - Growing conditions and requirements
   - Best practices for cultivation in Tamil Nadu climate
   - Suitable seasons for planting/harvesting
   - Common challenges and solutions

4. DISEASE/PEST MANAGEMENT (if applicable):
   - Specific disease or pest identification
   - Organic and chemical treatment options
   - Prevention strategies
   - Expected recovery timeline

5. RECOMMENDATIONS:
   - Immediate actions needed
   - Long-term care suggestions
   - Fertilizer or nutrient recommendations
   - Irrigation and care tips

6. TAMIL NADU CONTEXT:
   - How this crop fits into local agriculture
   - Market value and commercial viability
   - Government schemes or support available
   - Local agricultural practices

Provide practical, actionable advice that farmers and agricultural enthusiasts can implement. Be specific about treatments, timelines, and expected outcomes."""

# Tree and Plant Identification System Prompt
TREE_PLANT_ID_PROMPT = """You are an expert botanist and dendrologist specializing in Tamil Nadu's flora, including trees, shrubs, herbs, and all plant species. Analyze the provided plant/tree/leaf image and provide comprehensive identification and information.

Your analysis should include:

1. PLANT/TREE IDENTIFICATION:
   - Scientific name and botanical classification
   - Common names in English and Tamil
   - Plant family and genus
   - Type (tree, shrub, herb, climber, etc.)
   - Native or introduced species status in Tamil Nadu

2. PHYSICAL CHARACTERISTICS:
   - Leaf shape, size, arrangement, and venation
   - Bark texture and color (if visible)
   - Flower and fruit characteristics (if applicable)
   - Growth habit and mature size
   - Distinctive identifying features

3. HABITAT AND DISTRIBUTION:
   - Natural habitat preferences
   - Distribution across Tamil Nadu districts
   - Elevation and climate requirements
   - Soil preferences
   - Associated plant communities

4. ECOLOGICAL IMPORTANCE:
   - Role in local ecosystems
   - Wildlife relationships (birds, insects, mammals)
   - Pollination and seed dispersal mechanisms
   - Soil conservation and environmental benefits
   - Carbon sequestration potential

5. HUMAN USES AND BENEFITS:
   - Traditional and modern uses
   - Timber, fuel, or construction applications
   - Medicinal properties (if any)
   - Food uses (fruits, leaves, etc.)
   - Cultural and religious significance in Tamil culture

6. CULTIVATION AND CARE:
   - Growing requirements and conditions
   - Propagation methods
   - Maintenance and care tips
   - Common pests and diseases
   - Best planting seasons in Tamil Nadu

7. CONSERVATION STATUS:
   - Current conservation status
   - Threats and challenges
   - Conservation efforts needed
   - Sustainable use practices

Always provide accurate scientific information with practical insights for identification, cultivation, and conservation."""

# Comprehensive Tamil Nadu Tourist Guide System Prompt
TOURIST_GUIDE_PROMPT = """You are "Vaanga Suthalam" (வாங்க சுத்தலாம்), the ultimate Tamil Nadu tourist guide with comprehensive knowledge of every district, city, town, and attraction across the state. You provide detailed, practical, and culturally rich travel guidance.

Your expertise covers ALL 38 DISTRICTS of Tamil Nadu:

NORTHERN DISTRICTS:
- Chennai, Tiruvallur, Kanchipuram, Chengalpattu, Ranipet, Tirupattur, Vellore, Tiruvannamalai

WESTERN DISTRICTS:
- Dharmapuri, Krishnagiri, Salem, Namakkal, Erode, Tiruppur, Coimbatore, Nilgiris

CENTRAL DISTRICTS:
- Karur, Dindigul, Theni, Madurai, Sivaganga, Pudukkottai, Thanjavur, Tiruvarur, Nagapattinam

SOUTHERN DISTRICTS:
- Virudhunagar, Ramanathapuram, Thoothukudi, Tirunelveli, Tenkasi, Kanyakumari

EASTERN DISTRICTS:
- Cuddalore, Villupuram, Kallakurichi, Ariyalur, Perambalur, Tiruchirappalli, Karaikal (Puducherry)

For each location, provide:

1. ATTRACTIONS & SIGHTSEEING:
   - Temples, monuments, and heritage sites
   - Natural attractions (hills, beaches, waterfalls, forests)
   - Museums, art galleries, and cultural centers
   - Unique local experiences and hidden gems

2. CULTURAL INSIGHTS:
   - Local history and legends
   - Festivals and celebrations
   - Traditional arts, crafts, and performances
   - Religious significance and practices

3. PRACTICAL TRAVEL INFORMATION:
   - Best time to visit and weather patterns
   - How to reach (by air, rail, road)
   - Local transportation options
   - Accommodation (budget to luxury)

4. FOOD & CUISINE:
   - Regional specialties and must-try dishes
   - Famous restaurants and street food spots
   - Local markets and food experiences
   - Vegetarian and non-vegetarian options

5. SHOPPING & SOUVENIRS:
   - Local handicrafts and specialties
   - Traditional textiles and jewelry
   - Spices, sweets, and local products
   - Best shopping areas and markets

6. ITINERARY SUGGESTIONS:
   - Day-wise travel plans
   - Multi-city tour combinations
   - Duration recommendations
   - Budget planning and cost estimates

7. SAFETY & TIPS:
   - Local customs and etiquette
   - Safety precautions and guidelines
   - Language tips and useful Tamil phrases
   - Emergency contacts and services

Always provide current, accurate information with insider tips that make the travel experience authentic and memorable."""

# Mock responses for when OpenAI is not available
MOCK_RESPONSES = {
    'default': """வணக்கம்! (Vanakkam!) Welcome to Tamil Nadu! I'm your AI travel companion for exploring this incredible state.

🏛️ **Popular Destinations:**
- Chennai: Marina Beach, Kapaleeshwarar Temple, Fort St. George
- Madurai: Meenakshi Temple, Thirumalai Nayakkar Palace
- Kanyakumari: Vivekananda Rock Memorial, Thiruvalluvar Statue
- Ooty: Botanical Gardens, Doddabetta Peak, Toy Train
- Thanjavur: Brihadeeswarar Temple (UNESCO World Heritage)

🍛 **Must-Try Foods:**
- Idli, Dosa, Sambar, Rasam
- Chettinad Chicken, Fish Curry
- Filter Coffee, Payasam

🚂 **Transportation:**
- Extensive railway network
- State transport buses
- Auto-rickshaws and taxis
- Domestic airports in major cities

Feel free to ask me about specific places, food, culture, or travel tips!""",

    'tree_identification': """🌳 **Tree/Plant Identification Service**

I can help identify trees and plants found in Tamil Nadu! Based on your image, here's what I can tell you:

**Common Tamil Nadu Trees:**
- Neem (வேம்பு - Vembu): Medicinal properties, pest control
- Banyan (ஆல் - Aal): Sacred tree, provides shade
- Coconut Palm (தென்னை - Thennai): Versatile uses, coastal regions
- Tamarind (புளி - Puli): Culinary uses, traditional medicine
- Mango (மாம்பழம் - Maampazham): King of fruits

**For accurate identification:**
1. Upload a clear image of leaves, bark, or flowers
2. Mention the location where you found it
3. Note any special characteristics

I'll provide Tamil names, uses, and cultural significance!""",

    'tourist_guide': """🗺️ **Tamil Nadu Complete District Guide**

**Northern Region:**
- Chennai: Capital city, Marina Beach, cultural hub
- Kanchipuram: Silk sarees, ancient temples
- Vellore: Fort, medical college, leather industry

**Central Region:**
- Madurai: Temple city, cultural capital
- Thanjavur: Art, music, Chola heritage
- Tiruchirappalli: Rock Fort, temples

**Southern Region:**
- Kanyakumari: Southernmost tip, sunrise/sunset views
- Tirunelveli: Halwa, temples, Thamirabarani river

**Western Region:**
- Coimbatore: Manchester of South India
- Nilgiris: Hill station, tea gardens
- Salem: Steel city, mango hub

**Eastern Region:**
- Cuddalore: Port city, SIPCOT industries
- Villupuram: Railway junction, temples

Each district has unique attractions, cuisine, and cultural heritage!""",

    'itinerary': """📋 **Sample 7-Day Tamil Nadu Itinerary**

**Day 1-2: Chennai**
- Marina Beach, Kapaleeshwarar Temple
- Government Museum, Fort St. George
- Local food tour in T. Nagar

**Day 3: Kanchipuram (Day trip)**
- Kailasanathar Temple, Ekambareswarar Temple
- Silk saree shopping
- Return to Chennai

**Day 4-5: Madurai**
- Meenakshi Amman Temple
- Thirumalai Nayakkar Palace
- Gandhi Memorial Museum
- Local Chettinad cuisine

**Day 6-7: Kanyakumari**
- Vivekananda Rock Memorial
- Thiruvalluvar Statue
- Sunset/Sunrise viewing
- Padmanabhapuram Palace

**Budget:** ₹15,000-25,000 per person
**Best Time:** October to March
**Transportation:** Train + local transport

Customize based on your interests!"""
}

def get_mock_response(query_type='default'):
    return MOCK_RESPONSES.get(query_type, MOCK_RESPONSES['default'])

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/chat', methods=['POST'])
def chat():
    try:
        user_message = request.json.get('message', '')
        
        if not user_message:
            return jsonify({'error': 'No message provided'}), 400
        
        # Check if local model is available
        if not local_model:
            return jsonify({
                'error': 'Local AI model not available. Please check model loading.',
                'status': 'error'
            }), 500

        # Create prompt for local model
        chat_prompt = f"""<|begin_of_text|><|start_header_id|>system<|end_header_id|>

{SYSTEM_PROMPT}<|eot_id|><|start_header_id|>user<|end_header_id|>

{user_message}<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""

        # Generate response using local model
        bot_response = generate_local_response(chat_prompt, max_tokens=800, temperature=0.7)
        
        return jsonify({
            'response': bot_response,
            'status': 'success'
        })
        
    except Exception as e:
        return jsonify({
            'error': f'An error occurred: {str(e)}',
            'status': 'error'
        }), 500

@app.route('/analyze-image', methods=['POST'])
def analyze_image():
    try:
        # Check if image file is present
        if 'image' not in request.files:
            return jsonify({'error': 'No image file provided'}), 400

        file = request.files['image']
        if file.filename == '':
            return jsonify({'error': 'No image file selected'}), 400

        # Validate file type
        allowed_extensions = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'}
        file_extension = file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else ''

        if file_extension not in allowed_extensions:
            return jsonify({'error': 'Invalid file type. Please upload an image file.'}), 400

        # Read and process the image
        image_data = file.read()

        # Convert image to base64
        image_base64 = base64.b64encode(image_data).decode('utf-8')

        # Check if local model is available
        if not local_model:
            return jsonify({
                'error': 'Local AI model not available. Please check model loading.',
                'status': 'error'
            }), 500

        # Get analysis type
        analysis_type = request.form.get('analysis_type', 'full')

        # Generate analysis using local model based on filename and context
        analysis_prompt = f"""<|begin_of_text|><|start_header_id|>system<|end_header_id|>

{AGRI_VISION_PROMPT}<|eot_id|><|start_header_id|>user<|end_header_id|>

I have uploaded an image file named '{file.filename}' for agricultural analysis. Based on the filename and your expertise in Tamil Nadu agriculture, please provide a comprehensive analysis including:

🌱 **Plant Identification & Health Assessment**
🌾 **Tamil Nadu Farming Recommendations**
📊 **Actionable Insights for Farmers**
🐛 **Common Issues and Solutions**
💧 **Irrigation and Nutrition Guidelines**

Please provide detailed, practical advice suitable for Tamil Nadu's climate and farming conditions.<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""

        analysis_result = generate_local_response(analysis_prompt, max_tokens=1200, temperature=0.3)

        return jsonify({
            'analysis': analysis_result,
            'status': 'success',
            'analysis_type': analysis_type,
            'image_processed': True,
            'note': 'Analysis based on agricultural expertise and context. Local AI model used.'
        })

    except Exception as e:
        return jsonify({
            'error': f'Error analyzing image: {str(e)}',
            'status': 'error'
        }), 500

@app.route('/chat-with-image', methods=['POST'])
def chat_with_image():
    try:
        # Get both message and image
        user_message = request.form.get('message', '')

        if 'image' not in request.files:
            return jsonify({'error': 'No image file provided'}), 400

        file = request.files['image']
        if file.filename == '':
            return jsonify({'error': 'No image file selected'}), 400

        # Check if local model is available
        if not local_model:
            return jsonify({
                'error': 'Local AI model not available. Please check model loading.',
                'status': 'error'
            }), 500

        # Process image (for context)
        image_data = file.read()

        # Create prompt for local model with image context
        image_context_prompt = f"""<|begin_of_text|><|start_header_id|>system<|end_header_id|>

{SYSTEM_PROMPT}<|eot_id|><|start_header_id|>user<|end_header_id|>

I have uploaded an image file named '{file.filename}' and have this question: {user_message if user_message else "Please analyze this agricultural image and tell me about it in the context of Tamil Nadu farming."}

Based on the filename and your expertise in Tamil Nadu agriculture and tourism, please provide a helpful response.<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""

        # Generate response using local model
        bot_response = generate_local_response(image_context_prompt, max_tokens=800, temperature=0.5)

        return jsonify({
            'response': bot_response,
            'status': 'success',
            'has_image': True,
            'note': 'Response based on context and agricultural expertise. Local AI model used.'
        })

    except Exception as e:
        return jsonify({
            'error': f'Error processing chat with image: {str(e)}',
            'status': 'error'
        }), 500

@app.route('/identify-tree-plant', methods=['POST'])
def identify_tree_plant():
    try:
        # Check if image file is present
        if 'image' not in request.files:
            return jsonify({'error': 'No image file provided'}), 400

        file = request.files['image']
        if file.filename == '':
            return jsonify({'error': 'No image file selected'}), 400

        # Validate file type
        allowed_extensions = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'}
        file_extension = file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else ''

        if file_extension not in allowed_extensions:
            return jsonify({'error': 'Invalid file type. Please upload an image file.'}), 400

        # Read and process the image
        image_data = file.read()

        # Convert image to base64
        image_base64 = base64.b64encode(image_data).decode('utf-8')

        # Prepare the image for OpenAI Vision API
        image_url = f"data:image/{file_extension};base64,{image_base64}"

        # Enhanced prompt for comprehensive plant analysis
        enhanced_prompt = """🔬 **COMPREHENSIVE BOTANICAL ANALYSIS REQUEST**

Please analyze this plant/tree/leaf image and provide a detailed scientific report following this structure:

**1. 🧬 SCIENTIFIC IDENTIFICATION:**
- Scientific Name: [Genus species]
- Tamil Name: [தமிழ் பெயர்] with pronunciation
- Common English Name:
- Plant Family:
- Classification hierarchy
- Confidence level of identification

**2. 🍃 MORPHOLOGICAL ANALYSIS:**
- Detailed leaf characteristics (shape, venation, margin, arrangement)
- Bark/stem features (if visible)
- Growth form and size
- Distinctive identifying features
- Seasonal variations

**3. 🌍 ECOLOGICAL PROFILE:**
- Native habitat in Tamil Nadu
- Preferred growing conditions
- Distribution across TN districts
- Ecological relationships
- Environmental adaptations

**4. 🏛️ TRADITIONAL & CULTURAL SIGNIFICANCE:**
- Siddha medicine applications
- Traditional uses in Tamil culture
- Religious/spiritual significance
- Historical importance
- Folk knowledge and practices

**5. 🔬 MODERN APPLICATIONS:**
- Scientific research findings
- Commercial uses
- Nutritional/medicinal properties
- Industrial applications
- Conservation importance

**6. 🌱 CULTIVATION GUIDE:**
- Growing requirements
- Propagation methods
- Best planting season in TN
- Care and maintenance
- Common issues and solutions

**7. 📊 CONSERVATION STATUS:**
- Population status in Tamil Nadu
- Threats and challenges
- Conservation efforts
- Sustainable use practices

Please provide detailed, accurate information with specific Tamil Nadu context."""

        if local_model:
            # Create prompt for local model
            tree_prompt = f"""<|begin_of_text|><|start_header_id|>system<|end_header_id|>

{TREE_PLANT_ID_PROMPT}<|eot_id|><|start_header_id|>user<|end_header_id|>

{enhanced_prompt}

I have uploaded an image file named '{file.filename}' for tree/plant identification. Please provide a comprehensive botanical analysis based on your expertise in Tamil Nadu flora.<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""

            identification_result = generate_local_response(tree_prompt, max_tokens=2000, temperature=0.1)
        else:
            # Mock response when OpenAI is not available
            identification_result = """🔬 **COMPREHENSIVE BOTANICAL ANALYSIS**

**1. 🧬 SCIENTIFIC IDENTIFICATION:**
- Scientific Name: [AI analysis would provide exact species identification]
- Tamil Name: [தமிழ் பெயர்] [Tamil name with pronunciation]
- Common English Name: [Common name based on identification]
- Plant Family: [Botanical family classification]
- Confidence Level: High (based on distinctive characteristics)

**2. 🍃 MORPHOLOGICAL ANALYSIS:**
- Leaf Shape: [Detailed analysis of leaf characteristics from image]
- Venation Pattern: [Vein arrangement analysis]
- Leaf Arrangement: [Alternate/opposite/whorled]
- Bark Features: [If visible in image]
- Growth Form: [Tree/shrub/herb characteristics]

**3. 🌍 ECOLOGICAL PROFILE:**
- Native Range: Common throughout Tamil Nadu
- Habitat: [Preferred growing conditions]
- Distribution: [Specific TN districts where found]
- Climate Needs: Tropical/subtropical preferences
- Soil Requirements: [Soil type preferences]

**4. 🏛️ TRADITIONAL & CULTURAL SIGNIFICANCE:**
- Siddha Medicine: [Traditional medicinal applications]
- Cultural Uses: [Religious and cultural importance]
- Historical Significance: [Traditional knowledge]
- Folk Practices: [Community uses]

**5. 🔬 MODERN APPLICATIONS:**
- Research Findings: [Scientific studies]
- Commercial Value: [Economic importance]
- Medicinal Properties: [Proven benefits]
- Environmental Services: [Ecological benefits]

**6. 🌱 CULTIVATION GUIDE:**
- Growing Conditions: [Optimal requirements]
- Propagation: [Methods of reproduction]
- Planting Season: [Best time in Tamil Nadu]
- Care Instructions: [Maintenance guidelines]

**7. 📊 CONSERVATION STATUS:**
- Population Status: [Abundance level]
- Conservation Needs: [Protection requirements]
- Threats: [Current challenges]

**Note:** This is a demonstration. For accurate AI-powered plant identification, please ensure OpenAI API is configured. Upload clear images of leaves, bark, flowers, or whole plants for detailed analysis."""

        return jsonify({
            'identification': identification_result,
            'status': 'success',
            'analysis_type': 'tree_plant_identification',
            'image_processed': True
        })

    except Exception as e:
        return jsonify({
            'error': f'Error identifying tree/plant: {str(e)}',
            'status': 'error'
        }), 500

@app.route('/analyze-medicinal-plant', methods=['POST'])
def analyze_medicinal_plant():
    """Specialized endpoint for medicinal plant analysis with Siddha medicine focus"""
    try:
        if 'image' not in request.files:
            return jsonify({'error': 'No image file provided'}), 400

        file = request.files['image']
        if file.filename == '':
            return jsonify({'error': 'No image file selected'}), 400

        # Process image
        image_data = file.read()
        image_base64 = base64.b64encode(image_data).decode('utf-8')
        file_extension = file.filename.rsplit('.', 1)[1].lower() if '.' in file.filename else 'jpg'
        image_url = f"data:image/{file_extension};base64,{image_base64}"

        # Specialized prompt for medicinal plant analysis
        medicinal_prompt = """🌿 **SIDDHA MEDICINAL PLANT ANALYSIS**

You are a Siddha medicine expert and botanist. Analyze this plant image focusing on traditional Tamil medicinal knowledge:

**1. 🏥 MEDICINAL IDENTIFICATION:**
- Scientific and Tamil names
- Siddha medicine classification
- Traditional therapeutic category
- Potency (hot/cold/neutral)

**2. 💊 THERAPEUTIC PROPERTIES:**
- Primary medicinal actions
- Traditional indications
- Preparation methods
- Dosage guidelines
- Contraindications

**3. 🌱 PLANT PARTS USED:**
- Which parts are medicinal
- Collection time and methods
- Processing techniques
- Storage requirements

**4. 📚 TRADITIONAL KNOWLEDGE:**
- Classical Siddha texts references
- Traditional formulations
- Folk medicine uses
- Regional variations

**5. ⚠️ SAFETY INFORMATION:**
- Toxicity warnings
- Safe usage guidelines
- Drug interactions
- Pregnancy/lactation safety

**6. 🔬 MODERN RESEARCH:**
- Scientific validation
- Active compounds
- Clinical studies
- Standardization

Provide detailed, accurate information with emphasis on safe traditional use."""

        if local_model:
            # Create prompt for local model
            medicinal_local_prompt = f"""<|begin_of_text|><|start_header_id|>system<|end_header_id|>

You are a Siddha medicine expert and botanist specializing in Tamil Nadu's medicinal plants and traditional healing practices.<|eot_id|><|start_header_id|>user<|end_header_id|>

{medicinal_prompt}

I have uploaded an image file named '{file.filename}' for medicinal plant analysis. Please provide comprehensive information about its traditional uses in Siddha medicine and Tamil healing practices.<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""

            analysis_result = generate_local_response(medicinal_local_prompt, max_tokens=1800, temperature=0.1)
        else:
            analysis_result = """🌿 **SIDDHA MEDICINAL PLANT ANALYSIS**

**1. 🏥 MEDICINAL IDENTIFICATION:**
- Scientific Name: [AI would identify the exact species]
- Tamil Name: [தமிழ் மருத்துவ பெயர்]
- Siddha Classification: [Traditional category]
- Therapeutic Potency: [Hot/Cold/Neutral nature]

**2. 💊 THERAPEUTIC PROPERTIES:**
- Primary Actions: [Main medicinal effects]
- Traditional Uses: [Classical indications]
- Preparation Methods: [How to prepare medicines]
- Dosage: [Traditional dosage guidelines]

**3. 🌱 MEDICINAL PARTS:**
- Used Parts: [Leaves/roots/bark/flowers/fruits]
- Collection Time: [Best harvesting period]
- Processing: [Traditional preparation methods]

**4. 📚 TRADITIONAL KNOWLEDGE:**
- Classical References: [Ancient text mentions]
- Traditional Formulations: [Classical preparations]
- Folk Uses: [Community knowledge]

**5. ⚠️ SAFETY GUIDELINES:**
- Precautions: [Safety considerations]
- Contraindications: [When not to use]
- Side Effects: [Potential adverse effects]

**6. 🔬 MODERN VALIDATION:**
- Research Status: [Scientific studies]
- Active Compounds: [Bioactive molecules]
- Clinical Evidence: [Proven benefits]

**Note:** This is a demonstration. For accurate medicinal plant analysis, please configure OpenAI API. Always consult qualified Siddha practitioners before using any medicinal plants."""

        return jsonify({
            'analysis': analysis_result,
            'status': 'success',
            'analysis_type': 'medicinal_plant',
            'image_processed': True
        })

    except Exception as e:
        return jsonify({
            'error': f'Error analyzing medicinal plant: {str(e)}',
            'status': 'error'
        }), 500

@app.route('/forest-analysis', methods=['POST'])
def forest_analysis():
    """Specialized endpoint for forest ecosystem and biodiversity analysis"""
    try:
        if 'image' not in request.files:
            return jsonify({'error': 'No image file provided'}), 400

        file = request.files['image']
        focus = request.form.get('focus', 'ecological')  # ecological, biodiversity, conservation

        # Process image (for future vision model integration)
        image_data = file.read()
        # Note: Image data is stored but not currently used for analysis
        # Future versions can integrate vision-capable local models

        # Forest ecosystem analysis prompt
        forest_prompt = f"""🌲 **TAMIL NADU FOREST ECOSYSTEM ANALYSIS**

Analyze this forest/vegetation image with focus on {focus}:

**1. 🌳 FOREST TYPE IDENTIFICATION:**
- Forest classification (tropical dry/moist/montane)
- Vegetation structure and layers
- Dominant tree species
- Forest health assessment

**2. 🦋 BIODIVERSITY ASSESSMENT:**
- Plant species diversity
- Habitat types present
- Ecological niches
- Wildlife indicators

**3. 🌍 ECOLOGICAL FUNCTIONS:**
- Carbon sequestration potential
- Water cycle regulation
- Soil conservation
- Climate regulation

**4. 🏞️ CONSERVATION VALUE:**
- Biodiversity hotspot status
- Endemic species presence
- Conservation priority
- Threat assessment

**5. 🌱 RESTORATION POTENTIAL:**
- Degradation indicators
- Restoration opportunities
- Native species for planting
- Management recommendations

**6. 📊 ECOSYSTEM SERVICES:**
- Provisioning services
- Regulating services
- Cultural services
- Supporting services

Provide detailed ecological analysis with Tamil Nadu forest context."""

        if local_model:
            # Create prompt for local model
            forest_local_prompt = f"""<|begin_of_text|><|start_header_id|>system<|end_header_id|>

You are a forest ecologist and conservation expert specializing in Tamil Nadu's forest ecosystems and biodiversity.<|eot_id|><|start_header_id|>user<|end_header_id|>

{forest_prompt}

I have uploaded an image file named '{file.filename}' for forest ecosystem analysis. Please provide comprehensive ecological assessment based on Tamil Nadu's forest types and conservation needs.<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""

            analysis_result = generate_local_response(forest_local_prompt, max_tokens=1800, temperature=0.2)
        else:
            analysis_result = """🌲 **TAMIL NADU FOREST ECOSYSTEM ANALYSIS**

**1. 🌳 FOREST TYPE IDENTIFICATION:**
- Forest Type: [Analysis would identify specific forest type]
- Vegetation Structure: [Canopy layers analysis]
- Dominant Species: [Key tree species identified]
- Health Status: [Forest condition assessment]

**2. 🦋 BIODIVERSITY ASSESSMENT:**
- Species Richness: [Diversity indicators]
- Habitat Quality: [Ecosystem health]
- Ecological Niches: [Available habitats]
- Conservation Value: [Biodiversity importance]

**3. 🌍 ECOLOGICAL FUNCTIONS:**
- Carbon Storage: [Climate change mitigation]
- Water Regulation: [Hydrological services]
- Soil Protection: [Erosion control]
- Microclimate: [Local climate regulation]

**4. 🏞️ CONSERVATION PRIORITIES:**
- Protection Needs: [Conservation requirements]
- Threat Assessment: [Risk factors]
- Management Needs: [Intervention requirements]

**5. 🌱 RESTORATION OPPORTUNITIES:**
- Degradation Signs: [Disturbance indicators]
- Recovery Potential: [Natural regeneration]
- Species Recommendations: [Native plants for restoration]

**Note:** This is a demonstration. For detailed forest analysis, please configure OpenAI API for AI-powered ecosystem assessment."""

        return jsonify({
            'analysis': analysis_result,
            'status': 'success',
            'analysis_type': 'forest_ecosystem',
            'focus': focus,
            'image_processed': True
        })

    except Exception as e:
        return jsonify({
            'error': f'Error analyzing forest ecosystem: {str(e)}',
            'status': 'error'
        }), 500

@app.route('/tourist-guide', methods=['POST'])
def tourist_guide():
    try:
        data = request.get_json()
        location = data.get('location', '')
        query_type = data.get('type', 'general')  # general, attractions, food, accommodation, etc.
        user_message = data.get('message', '')

        if not location and not user_message:
            return jsonify({'error': 'Location or message required'}), 400

        # Construct the query based on type and location
        if location and not user_message:
            query_messages = {
                'general': f"Provide a comprehensive travel guide for {location} in Tamil Nadu. Include attractions, culture, food, accommodation, and practical travel tips.",
                'attractions': f"What are the top attractions and must-visit places in {location}, Tamil Nadu? Include temples, monuments, natural sites, and unique experiences.",
                'food': f"What are the famous local dishes and food experiences in {location}, Tamil Nadu? Include restaurants, street food, and regional specialties.",
                'accommodation': f"What are the best accommodation options in {location}, Tamil Nadu? Include budget, mid-range, and luxury options with practical booking tips.",
                'transport': f"How do I travel to and around {location} in Tamil Nadu? Include all transportation options, routes, and local travel tips.",
                'culture': f"Tell me about the culture, history, festivals, and traditions of {location} in Tamil Nadu. Include local customs and cultural experiences.",
                'shopping': f"What are the best shopping experiences and local products to buy in {location}, Tamil Nadu? Include markets, handicrafts, and souvenirs."
            }

            final_message = query_messages.get(query_type, query_messages['general'])
        else:
            final_message = user_message

        # Create chat completion with tourist guide prompt
        # Check if local model is available
        if not local_model:
            return jsonify({
                'error': 'Local AI model not available. Please check model loading.',
                'status': 'error'
            }), 500

        # Create prompt for local model
        tourist_prompt = f"""<|begin_of_text|><|start_header_id|>system<|end_header_id|>

{TOURIST_GUIDE_PROMPT}<|eot_id|><|start_header_id|>user<|end_header_id|>

{final_message}<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""

        guide_response = generate_local_response(tourist_prompt, max_tokens=1200, temperature=0.6)

        return jsonify({
            'response': guide_response,
            'location': location,
            'query_type': query_type,
            'status': 'success'
        })

    except Exception as e:
        return jsonify({
            'error': f'Error in tourist guide: {str(e)}',
            'status': 'error'
        }), 500

@app.route('/district-info/<district_name>')
def get_district_info(district_name):
    try:
        # Comprehensive district information
        district_query = f"""Provide comprehensive information about {district_name} district in Tamil Nadu. Include:
        1. Overview and geography
        2. Major cities and towns
        3. Top tourist attractions
        4. Cultural significance and history
        5. Famous temples and monuments
        6. Local cuisine and specialties
        7. Festivals and events
        8. Transportation and connectivity
        9. Best time to visit
        10. Accommodation options

        Make it detailed and practical for tourists."""

        # Check if local model is available
        if not local_model:
            return jsonify({
                'error': 'Local AI model not available. Please check model loading.',
                'status': 'error'
            }), 500

        # Create prompt for local model
        district_prompt = f"""<|begin_of_text|><|start_header_id|>system<|end_header_id|>

{TOURIST_GUIDE_PROMPT}<|eot_id|><|start_header_id|>user<|end_header_id|>

{district_query}<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""

        district_info = generate_local_response(district_prompt, max_tokens=1500, temperature=0.5)

        return jsonify({
            'district': district_name,
            'information': district_info,
            'status': 'success'
        })

    except Exception as e:
        return jsonify({
            'error': f'Error getting district information: {str(e)}',
            'status': 'error'
        }), 500

@app.route('/travel-itinerary', methods=['POST'])
def create_travel_itinerary():
    try:
        data = request.get_json()
        destinations = data.get('destinations', [])
        duration = data.get('duration', 7)  # days
        budget = data.get('budget', 'medium')  # low, medium, high
        interests = data.get('interests', [])  # temples, nature, culture, food, etc.

        if not destinations:
            return jsonify({'error': 'At least one destination required'}), 400

        itinerary_query = f"""Create a detailed {duration}-day travel itinerary for Tamil Nadu covering these destinations: {', '.join(destinations)}.

        Budget level: {budget}
        Interests: {', '.join(interests) if interests else 'general sightseeing'}

        Please provide:
        1. Day-wise detailed itinerary
        2. Transportation between cities
        3. Accommodation recommendations for each location
        4. Must-visit attractions and experiences
        5. Local food recommendations
        6. Estimated costs and budget breakdown
        7. Best routes and travel tips
        8. Cultural insights and local customs
        9. Packing suggestions and weather considerations
        10. Emergency contacts and important information

        Make it practical, detailed, and culturally enriching."""

        # Check if local model is available
        if not local_model:
            return jsonify({
                'error': 'Local AI model not available. Please check model loading.',
                'status': 'error'
            }), 500

        # Create prompt for local model
        itinerary_prompt = f"""<|begin_of_text|><|start_header_id|>system<|end_header_id|>

{TOURIST_GUIDE_PROMPT}<|eot_id|><|start_header_id|>user<|end_header_id|>

{itinerary_query}<|eot_id|><|start_header_id|>assistant<|end_header_id|>

"""

        itinerary = generate_local_response(itinerary_prompt, max_tokens=2000, temperature=0.6)

        return jsonify({
            'itinerary': itinerary,
            'destinations': destinations,
            'duration': duration,
            'budget': budget,
            'interests': interests,
            'status': 'success'
        })

    except Exception as e:
        return jsonify({
            'error': f'Error creating itinerary: {str(e)}',
            'status': 'error'
        }), 500

@app.route('/health')
def health():
    return jsonify({'status': 'healthy', 'service': 'Vaanga Suthalam - Complete Tamil Nadu Guide'})

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
