<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>Vaanga Suthalam - Enhanced Plant Analysis Demo</title>
    <link rel="stylesheet" href="static/style.css">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .demo-banner {
            background: linear-gradient(135deg, #FF6B35, #F7931E);
            color: white;
            padding: 15px;
            text-align: center;
            font-weight: 600;
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        .demo-features {
            background: #f8f9fa;
            padding: 20px;
            margin: 20px;
            border-radius: 12px;
            border: 2px solid #e9ecef;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        .feature-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #FF6B35;
        }
        .feature-card h4 {
            margin: 0 0 8px 0;
            color: #FF6B35;
        }
        .feature-card p {
            margin: 0;
            font-size: 0.9rem;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="demo-banner">
        🚀 ENHANCED PLANT ANALYSIS FEATURES - AI-Powered Botanical Intelligence
    </div>

    <div class="app-container">
        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-content">
                <div class="sidebar-header">
                    <div class="bot-avatar">
                        <i class="fas fa-leaf"></i>
                    </div>
                    <h2>Vaanga Suthalam</h2>
                    <p>Tamil Nadu Plant Expert</p>
                </div>

                <div class="demo-features">
                    <h3>🔬 New Analysis Features</h3>
                    <div class="feature-grid">
                        <div class="feature-card">
                            <h4>🌳 Tree/Plant ID</h4>
                            <p>Scientific names, Tamil names, botanical classification</p>
                        </div>
                        <div class="feature-card">
                            <h4>🌿 Medicinal Analysis</h4>
                            <p>Siddha medicine properties, traditional uses</p>
                        </div>
                        <div class="feature-card">
                            <h4>🌲 Forest Ecosystem</h4>
                            <p>Biodiversity assessment, conservation value</p>
                        </div>
                        <div class="feature-card">
                            <h4>🌱 Agricultural</h4>
                            <p>Crop health, growth stage, farming tips</p>
                        </div>
                        <div class="feature-card">
                            <h4>🐛 Disease Detection</h4>
                            <p>Pest identification, treatment recommendations</p>
                        </div>
                        <div class="feature-card">
                            <h4>🍃 Nutrition Status</h4>
                            <p>Deficiency analysis, fertilizer recommendations</p>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Feature Sections -->
                <div class="tree-identification">
                    <h3>🌳 Tree & Plant ID</h3>
                    <p>AI-powered botanical identification</p>
                    <button class="tree-id-btn" onclick="openImageModal()">
                        <i class="fas fa-search"></i>
                        <span>Identify Plant</span>
                    </button>
                </div>
                
                <div class="tourist-guide">
                    <h3>🗺️ Complete TN Guide</h3>
                    <p>Explore all 38 districts</p>
                    <button class="guide-btn" onclick="alert('Tourist guide feature ready!')">
                        <i class="fas fa-compass"></i>
                        <span>Explore Districts</span>
                    </button>
                </div>
                
                <div class="itinerary-planner">
                    <h3>📋 Trip Planner</h3>
                    <p>Custom travel itineraries</p>
                    <button class="itinerary-btn" onclick="alert('Itinerary planner ready!')">
                        <i class="fas fa-calendar-alt"></i>
                        <span>Plan Your Trip</span>
                    </button>
                </div>
            </div>
        </aside>

        <!-- Main Chat Area -->
        <main class="chat-main">
            <header class="chat-header">
                <div class="chat-header-info">
                    <button class="menu-toggle" onclick="toggleSidebar()">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="bot-avatar">
                        <i class="fas fa-leaf"></i>
                    </div>
                    <div class="bot-info">
                        <h2>Vaanga Suthalam</h2>
                        <span class="bot-status">🔬 Enhanced Plant Analysis Ready</span>
                    </div>
                </div>
                <div class="chat-actions">
                    <button class="action-icon" onclick="openImageModal()" title="Plant Analysis">
                        <i class="fas fa-camera"></i>
                    </button>
                    <button class="action-icon" onclick="alert('Voice feature ready!')" title="Voice Input">
                        <i class="fas fa-microphone"></i>
                    </button>
                </div>
            </header>

            <div class="chat-messages-container">
                <div class="welcome-card">
                    <div class="welcome-content">
                        <div class="welcome-icon">
                            <i class="fas fa-seedling"></i>
                        </div>
                        <h2>Enhanced Plant Analysis System</h2>
                        <p>Upload images of leaves, trees, or plants for comprehensive AI-powered botanical analysis with Tamil Nadu expertise!</p>
                        
                        <div class="welcome-suggestions">
                            <div class="suggestion-card" onclick="openImageModal()">
                                <i class="fas fa-search"></i>
                                <span>Identify Plant Species</span>
                            </div>
                            <div class="suggestion-card" onclick="openImageModal()">
                                <i class="fas fa-mortar-pestle"></i>
                                <span>Medicinal Properties</span>
                            </div>
                            <div class="suggestion-card" onclick="openImageModal()">
                                <i class="fas fa-tree"></i>
                                <span>Forest Analysis</span>
                            </div>
                            <div class="suggestion-card" onclick="openImageModal()">
                                <i class="fas fa-seedling"></i>
                                <span>Agricultural Analysis</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Enhanced Image Upload Modal -->
    <div class="modal-overlay" id="imageUploadModal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>🔬 Plant & Tree Analysis</h3>
                <button class="modal-close" onclick="closeImageModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="upload-area">
                    <div class="upload-content">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <h4>Upload Plant/Tree/Leaf Image</h4>
                        <p>Drag and drop an image here, or click to select</p>
                        <p class="upload-note">Supported formats: JPG, PNG, GIF, WebP (Max 10MB)</p>
                        <button class="upload-btn">
                            <i class="fas fa-folder-open"></i>
                            Select Image
                        </button>
                    </div>
                </div>

                <div class="analysis-options">
                    <h4>🔬 Choose Analysis Type</h4>
                    <div class="option-buttons">
                        <button class="option-btn active" data-type="tree_identification">
                            <i class="fas fa-search"></i>
                            <span>Tree/Plant ID</span>
                        </button>
                        <button class="option-btn" data-type="medicinal">
                            <i class="fas fa-mortar-pestle"></i>
                            <span>Medicinal Analysis</span>
                        </button>
                        <button class="option-btn" data-type="forest">
                            <i class="fas fa-tree"></i>
                            <span>Forest Ecosystem</span>
                        </button>
                        <button class="option-btn" data-type="full">
                            <i class="fas fa-seedling"></i>
                            <span>Agricultural Analysis</span>
                        </button>
                        <button class="option-btn" data-type="disease">
                            <i class="fas fa-bug"></i>
                            <span>Disease Detection</span>
                        </button>
                        <button class="option-btn" data-type="nutrition">
                            <i class="fas fa-leaf"></i>
                            <span>Nutrition Status</span>
                        </button>
                    </div>
                    <div class="analysis-description">
                        <p id="analysisDescription">🌳 Comprehensive plant identification with scientific names, Tamil names, characteristics, uses, and cultivation information.</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn-secondary" onclick="closeImageModal()">Cancel</button>
                <button class="btn-primary" onclick="alert('Analysis feature ready! Configure OpenAI API for full functionality.')">
                    <i class="fas fa-search"></i>
                    Analyze Image
                </button>
            </div>
        </div>
    </div>

    <script>
        function toggleSidebar() {
            document.getElementById('sidebar').classList.toggle('active');
        }

        function openImageModal() {
            document.getElementById('imageUploadModal').style.display = 'flex';
            document.body.style.overflow = 'hidden';
        }

        function closeImageModal() {
            document.getElementById('imageUploadModal').style.display = 'none';
            document.body.style.overflow = '';
        }

        // Analysis type selection with descriptions
        document.addEventListener('click', function(e) {
            if (e.target.closest('.option-btn')) {
                const btn = e.target.closest('.option-btn');
                document.querySelectorAll('.option-btn').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                const type = btn.getAttribute('data-type');
                updateAnalysisDescription(type);
            }
        });

        function updateAnalysisDescription(type) {
            const descriptions = {
                'tree_identification': '🌳 Comprehensive plant identification with scientific names, Tamil names, characteristics, uses, and cultivation information.',
                'medicinal': '🌿 Detailed analysis of medicinal properties, traditional Siddha medicine uses, therapeutic applications, and safety guidelines.',
                'forest': '🌲 Forest ecosystem analysis including biodiversity assessment, conservation value, and ecological functions.',
                'full': '🌱 Complete agricultural analysis including crop health, growth stage, and farming recommendations.',
                'disease': '🐛 Disease and pest identification with treatment recommendations and prevention strategies.',
                'nutrition': '🍃 Nutritional deficiency analysis with fertilizer recommendations and soil health assessment.'
            };
            
            const descriptionElement = document.getElementById('analysisDescription');
            if (descriptionElement && descriptions[type]) {
                descriptionElement.textContent = descriptions[type];
            }
        }

        // Close modal when clicking outside
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('modal-overlay')) {
                closeImageModal();
            }
        });
    </script>
</body>
</html>
