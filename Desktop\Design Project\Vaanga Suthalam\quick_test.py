#!/usr/bin/env python3
"""
Quick test to verify the app works with fallback responses
"""

def test_basic_functionality():
    """Test basic app functionality"""
    
    print("🧪 Testing Vaanga Suthalam - Quick Test")
    print("=" * 50)
    
    try:
        print("1. Testing imports...")
        import sys
        import os
        
        # Add current directory to path
        sys.path.insert(0, os.path.dirname(__file__))
        
        # Test basic imports
        from flask import Flask
        print("✅ Flask import successful")
        
        # Test app import
        print("2. Testing app import...")
        from app import app, local_model, model_loaded
        print("✅ App imported successfully")
        
        print(f"📊 Model loaded: {model_loaded}")
        print(f"🤖 Local model: {'Available' if local_model else 'Using fallback'}")
        
        print("3. Testing chat endpoint...")
        
        # Test chat with test client
        with app.test_client() as client:
            response = client.post('/chat', 
                                 json={'message': 'Hello, tell me about Chennai'},
                                 content_type='application/json')
            
            print(f"✅ Chat endpoint status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.get_json()
                if data and 'response' in data:
                    print("✅ Chat response received")
                    print(f"📝 Response: {data['response'][:200]}...")
                    
                    if 'Chennai' in data['response'] or 'Tamil Nadu' in data['response']:
                        print("✅ Response contains relevant content")
                    else:
                        print("⚠️  Response may not be contextually relevant")
                else:
                    print("❌ Invalid response format")
                    print(f"Response data: {data}")
            else:
                print(f"❌ Chat endpoint error: {response.status_code}")
                if response.data:
                    print(f"Error details: {response.data.decode()}")
        
        print("\n🎉 Basic functionality test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Error during test: {e}")
        import traceback
        traceback.print_exc()
        return False

def show_installation_guide():
    """Show installation guide for local model"""
    
    print("\n" + "="*60)
    print("📋 LOCAL MODEL INSTALLATION GUIDE")
    print("="*60)
    
    print("\n🔧 To install llama-cpp-python on Windows:")
    print("1. Install Visual Studio Build Tools:")
    print("   - Download from: https://visualstudio.microsoft.com/downloads/")
    print("   - Install 'C++ build tools' workload")
    print()
    print("2. Install llama-cpp-python:")
    print("   pip install llama-cpp-python")
    print()
    print("3. Alternative - Use pre-built wheels:")
    print("   pip install llama-cpp-python --extra-index-url https://abetlen.github.io/llama-cpp-python/whl/cpu")
    print()
    print("4. For now, the app works with intelligent fallback responses!")
    print()
    print("🚀 To run the app:")
    print("   python app.py")
    print("   Then open: http://localhost:5000")

if __name__ == "__main__":
    success = test_basic_functionality()
    
    if success:
        print("\n✅ SUCCESS! Your app is working with fallback responses.")
        show_installation_guide()
    else:
        print("\n❌ Test failed. Check the error details above.")
        print("\n🔧 Common fixes:")
        print("1. Install Flask: pip install flask flask-cors")
        print("2. Check Python version: python --version (need 3.8+)")
        print("3. Check file permissions and paths")
